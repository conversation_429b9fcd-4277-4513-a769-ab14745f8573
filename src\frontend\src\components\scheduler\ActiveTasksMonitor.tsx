import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CompactProgress } from '@/components/ui/enhanced-progress';
import { useAdminWebSocket } from '@/hooks/useAdminWebSocket';
import {
  Loader,
  Clock,
  User,
  Database,
  Zap,
  Play,
  Pause,
  X,
  Activity,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react';

import type { ActiveTask } from '@/types/scheduler';
import { Progress as ProgressType, RealTimeProgressUpdate } from '@/types/scanLogs';
import {
  TaskTypeColors,
  formatRelativeTime,
  getTaskTypeDisplayName,
  getProgressPercentage
} from '@/types/scheduler';

interface ActiveTasksMonitorProps {
  tasks: ActiveTask[];
  loading?: boolean;
  onRefresh?: () => void;
  onPauseTask?: (taskId: string) => void;
  onResumeTask?: (taskId: string) => void;
  onCancelTask?: (taskId: string) => void;
  enableRealTimeMonitoring?: boolean;
}

const ActiveTasksMonitor: React.FC<ActiveTasksMonitorProps> = ({
  tasks,
  loading = false,
  onRefresh,
  onPauseTask,
  onResumeTask,
  onCancelTask,
  enableRealTimeMonitoring = true
}) => {
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(enableRealTimeMonitoring);
  const [realTimeProgress, setRealTimeProgress] = useState<Map<string, ProgressType>>(new Map());

  // Admin WebSocket for real-time progress monitoring
  const {
    isConnected,
    isConnecting,
    error: wsError,
    lastUpdate,
    isAdminSubscribed,
    subscribeToAdminMonitoring,
    unsubscribeFromAdminMonitoring,
    connect
  } = useAdminWebSocket({
    autoConnect: isRealTimeEnabled
  });

  // Subscribe to admin monitoring when enabled
  useEffect(() => {
    if (isConnected && isRealTimeEnabled && !isAdminSubscribed) {
      subscribeToAdminMonitoring();
    } else if (isAdminSubscribed && !isRealTimeEnabled) {
      unsubscribeFromAdminMonitoring();
    }
  }, [isConnected, isRealTimeEnabled, isAdminSubscribed, subscribeToAdminMonitoring, unsubscribeFromAdminMonitoring]);

  // Handle real-time progress updates
  useEffect(() => {
    if (!lastUpdate || !isRealTimeEnabled) return;

    const update = lastUpdate;

    // Map scan updates to task progress
    if (update.progress && (update.eventType === 'progress' || update.eventType === 'file_progress')) {
      setRealTimeProgress(prev => {
        const newProgress = new Map(prev);

        // Try to match the scan to an active task
        const matchingTask = tasks.find(task =>
          task.type === 'scan' &&
          task.repository === update.repoId &&
          task.groupName === update.groupName
        );

        if (matchingTask && update.progress) {
          newProgress.set(matchingTask.id, update.progress);
        }

        return newProgress;
      });
    }

    // Clean up completed/failed scans
    if (update.eventType === 'scan_complete' || update.eventType === 'scan_error') {
      setRealTimeProgress(prev => {
        const newProgress = new Map(prev);
        const matchingTask = tasks.find(task =>
          task.type === 'scan' &&
          task.repository === update.repoId &&
          task.groupName === update.groupName
        );

        if (matchingTask) {
          newProgress.delete(matchingTask.id);
        }

        return newProgress;
      });
    }
  }, [lastUpdate, isRealTimeEnabled, tasks]);

  const toggleRealTimeMonitoring = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled);
    if (!isConnected && !isRealTimeEnabled) {
      connect();
    }
  };
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Currently Running Tasks</CardTitle>
          <CardDescription>
            Tasks that are currently being executed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <Loader className="h-8 w-8 animate-spin text-blue-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-green-600" />
              <span>Currently Running Tasks</span>
              <Badge variant="secondary">{tasks?.length || 0} active</Badge>
            </CardTitle>
            <CardDescription>
              Tasks that are currently being executed with real-time progress
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {/* Real-time monitoring toggle */}
            <Button
              variant={isRealTimeEnabled ? "default" : "outline"}
              size="sm"
              onClick={toggleRealTimeMonitoring}
              className="gap-1"
            >
              {isRealTimeEnabled ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              {isRealTimeEnabled ? 'Live' : 'Static'}
            </Button>

            {/* Connection status indicator */}
            {isRealTimeEnabled && (
              <div className="flex items-center gap-1 text-xs">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                {isConnecting ? 'Connecting...' : isConnected ? 'Live' : 'Offline'}
              </div>
            )}

            {/* Refresh button */}
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
            )}

            {/* Reconnect button if disconnected */}
            {isRealTimeEnabled && !isConnected && !isConnecting && (
              <Button size="sm" variant="outline" onClick={connect}>
                <RefreshCw className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {(tasks?.length || 0) === 0 ? (
          <div className="text-center py-12">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Tasks</h3>
            <p className="text-gray-500">
              All tasks are currently idle. New tasks will appear here when they start running.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {(tasks || []).map((task) => (
              <div
                key={task.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                {/* Task Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Badge className={`${TaskTypeColors[task.type]} border-0`}>
                      {getTaskTypeDisplayName(task.type)}
                    </Badge>
                    <Badge
                      variant="outline"
                      className={`${task.status === 'running' ? 'border-green-500 text-green-700' : 'border-yellow-500 text-yellow-700'}`}
                    >
                      <Loader className="h-3 w-3 mr-1 animate-spin" />
                      {task.status}
                    </Badge>
                    <h3 className="font-medium">{task.name}</h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">
                      Started {formatRelativeTime(task.startedAt)}
                    </span>
                    <div className="flex items-center space-x-1">
                      {onPauseTask && task.status === 'running' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onPauseTask(task.id)}
                          title="Pause Task"
                        >
                          <Pause className="h-4 w-4" />
                        </Button>
                      )}
                      {onResumeTask && task.status !== 'running' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onResumeTask(task.id)}
                          title="Resume Task"
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                      )}
                      {onCancelTask && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onCancelTask(task.id)}
                          title="Cancel Task"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                {(task.progress || realTimeProgress.has(task.id)) && (
                  <div className="mb-3">
                    {/* Use real-time progress if available, otherwise fall back to task progress */}
                    {realTimeProgress.has(task.id) && isRealTimeEnabled ? (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Activity className="h-3 w-3 text-green-500" />
                          <span>Live Progress</span>
                        </div>
                        <CompactProgress
                          progress={realTimeProgress.get(task.id)!}
                          className="mt-1"
                        />
                      </div>
                    ) : task.progress ? (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">
                            {task.progress.description || 'Processing...'}
                          </span>
                          <span className="text-sm text-gray-500">
                            {task.progress.current} / {task.progress.total} ({getProgressPercentage(task.progress)}%)
                          </span>
                        </div>
                        <Progress
                          value={getProgressPercentage(task.progress)}
                          className="h-2"
                        />
                      </div>
                    ) : null}
                  </div>
                )}

                {/* Task Details */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Database className="h-4 w-4" />
                    <div>
                      <span className="font-medium">Repository:</span>
                      <div>{task.repository || 'N/A'}</div>
                    </div>
                  </div>

                  {task.groupName && (
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <div>
                        <span className="font-medium">Group:</span>
                        <div>{task.groupName}</div>
                      </div>
                    </div>
                  )}

                  {task.workerId && (
                    <div className="flex items-center space-x-2">
                      <Zap className="h-4 w-4" />
                      <div>
                        <span className="font-medium">Worker:</span>
                        <div>{task.workerId}</div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <div>
                      <span className="font-medium">Started:</span>
                      <div>{new Date(task.startedAt).toLocaleTimeString()}</div>
                    </div>
                  </div>
                </div>

                {/* Metadata */}
                {task.metadata && Object.keys(task.metadata).length > 0 && (
                  <div className="mt-3 pt-3 border-t">
                    <details className="group">
                      <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                        Task Metadata ({Object.keys(task.metadata).length} items)
                      </summary>
                      <div className="mt-2 pl-4 space-y-1">
                        {Object.entries(task.metadata).map(([key, value]) => (
                          <div key={key} className="text-xs text-gray-600">
                            <span className="font-medium">{key}:</span> {JSON.stringify(value)}
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActiveTasksMonitor;
