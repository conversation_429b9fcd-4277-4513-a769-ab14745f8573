// Scan log types for usage tracking

export type ScanLogLevel = 'info' | 'warning' | 'error' | 'debug';

export type ScanStatus = 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';

export interface Progress {
  current: number;
  total: number;
  percentage: number;
  description?: string;

  // Enhanced progress tracking
  currentFile?: string;
  currentChunk?: number;
  totalChunks?: number;
  chunkSize?: number;
  filesPerSecond?: number;
  estimatedTimeRemaining?: number; // milliseconds
  startTime?: string;
  lastUpdateTime?: string;
}

export interface ScanLogEntry {
  id: string;
  scanId: string;
  repoId: string;
  groupName: string;
  timestamp: string;
  level: ScanLogLevel;
  message: string;
  details?: string;
  step?: string;
  progress?: Progress;
  sourceId?: string;
  sourceName?: string;
  error?: string;
  duration?: number; // milliseconds
  metadata?: Record<string, any>;
}

export interface CancellationInfo {
  cancelledAt: string;
  reason?: string;
  userId?: string;
}

export interface PerformanceMetrics {
  averageStepDuration: number; // milliseconds
  totalProcessingTime: number; // milliseconds
  itemsPerSecond: number;
  memoryUsage?: number; // bytes
  cpuUsage?: number; // percentage
}

export interface SourceScanStatus {
  sourceId: string;
  sourceName: string;
  sourceType: string;
  status: ScanStatus;
  startTime?: string;
  endTime?: string;
  duration?: number; // milliseconds
  resultCount: number;
  errorCount: number;
  lastError?: string;
}

export interface ScanLogSummary {
  scanId: string;
  repoId: string;
  groupName: string;
  status: ScanStatus;
  startTime: string;
  endTime?: string;
  duration?: number; // milliseconds
  totalSteps: number;
  completedSteps: number;
  sourcesScanned: number;
  totalSources: number;
  resultsFound: number;
  errorCount: number;
  warningCount: number;
  lastActivity: string;
  cancellationInfo?: CancellationInfo;
  performance?: PerformanceMetrics;
  sourceStatuses?: SourceScanStatus[];

  // Enhanced progress tracking
  currentProgress?: Progress;
  totalFiles?: number;
  processedFiles?: number;
  chunkedProcessing?: boolean;
  chunkSize?: number;
  currentChunk?: number;
  totalChunks?: number;
}

export interface ScanLogFilter {
  repoId?: string;
  groupName?: string;
  scanId?: string;
  level?: ScanLogLevel;
  status?: string;
  sourceId?: string;
  startTime?: string;
  endTime?: string;
  searchQuery?: string;
  page?: number;
  pageSize?: number;
}

export interface ScanLogResponse {
  logs: ScanLogEntry[];
  summary?: ScanLogSummary;
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ErrorSummary {
  error: string;
  count: number;
  lastOccurred: string;
  affectedGroups?: string[];
}

export interface OverallPerformance {
  averageScanDuration: number; // milliseconds
  successRate: number; // percentage
  totalResultsFound: number;
  averageResultsPerScan: number;
}

export interface ScanLogsOverview {
  totalScans: number;
  activeScans: number;
  completedScans: number;
  failedScans: number;
  recentActivity: ScanLogSummary[];
  topErrors?: ErrorSummary[];
  performanceStats?: OverallPerformance;
}

export interface ScanSummariesResponse {
  summaries: ScanLogSummary[];
  total: number;
}

// Enhanced interfaces for chunked processing and real-time updates

export interface ChunkedProcessingConfig {
  enabled: boolean;
  chunkSize: number; // Number of files per chunk
  maxConcurrentChunks: number; // Max parallel chunks
  progressUpdateInterval: number; // Update frequency in files
}

export interface FileProcessingStatus {
  filePath: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  duration?: number; // milliseconds
  resultsFound: number;
  error?: string;
  chunkIndex: number;
}

export interface ChunkProcessingStatus {
  chunkIndex: number;
  totalFiles: number;
  processedFiles: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  duration?: number; // milliseconds
  resultsFound: number;
  errorCount: number;
  files?: FileProcessingStatus[];
}

export interface RealTimeProgressUpdate {
  scanId: string;
  repoId: string;
  groupName: string;
  timestamp: string;
  progress?: Progress;
  currentChunk?: ChunkProcessingStatus;
  summary?: ScanLogSummary;
  eventType: 'scan_start' | 'progress' | 'file_progress' | 'chunk_start' | 'chunk_complete' | 'file_complete' | 'scan_complete' | 'scan_error' | 'error';
  message?: string;
  error?: string;
}

// Utility types for UI components
export interface ScanLogDisplayOptions {
  showTimestamps: boolean;
  showDetails: boolean;
  showProgress: boolean;
  showSourceInfo: boolean;
  groupByStep: boolean;
  autoRefresh: boolean;
  refreshInterval: number; // seconds
}

export interface ScanLogExportOptions {
  format: 'json' | 'csv' | 'txt';
  includeDetails: boolean;
  includeMetadata: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}

// Status display helpers
export const ScanStatusColors: Record<ScanStatus, string> = {
  queued: 'text-yellow-600 bg-yellow-50',
  running: 'text-blue-600 bg-blue-50',
  completed: 'text-green-600 bg-green-50',
  failed: 'text-red-600 bg-red-50',
  cancelled: 'text-gray-600 bg-gray-50',
};

export const ScanStatusIcons: Record<ScanStatus, string> = {
  queued: 'clock',
  running: 'loader',
  completed: 'check-circle',
  failed: 'x-circle',
  cancelled: 'x',
};

export const LogLevelColors: Record<ScanLogLevel, string> = {
  info: 'text-blue-600',
  warning: 'text-yellow-600',
  error: 'text-red-600',
  debug: 'text-gray-600',
};

export const LogLevelIcons: Record<ScanLogLevel, string> = {
  info: 'info',
  warning: 'alert-triangle',
  error: 'alert-circle',
  debug: 'bug',
};

// Helper functions
export const formatDuration = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }

  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

export const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};

export const formatRelativeTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();

  if (diffMs < 60000) { // Less than 1 minute
    return 'Just now';
  }

  if (diffMs < 3600000) { // Less than 1 hour
    const minutes = Math.floor(diffMs / 60000);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  }

  if (diffMs < 86400000) { // Less than 1 day
    const hours = Math.floor(diffMs / 3600000);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  }

  const days = Math.floor(diffMs / 86400000);
  return `${days} day${days > 1 ? 's' : ''} ago`;
};

export const getProgressPercentage = (progress?: Progress): number => {
  if (!progress || progress.total === 0) return 0;
  return Math.round((progress.current / progress.total) * 100);
};

export const getScanStatusText = (status: ScanStatus): string => {
  switch (status) {
    case 'queued':
      return 'Queued';
    case 'running':
      return 'Running';
    case 'completed':
      return 'Completed';
    case 'failed':
      return 'Failed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return 'Unknown';
  }
};

export const getLogLevelText = (level: ScanLogLevel): string => {
  switch (level) {
    case 'info':
      return 'Info';
    case 'warning':
      return 'Warning';
    case 'error':
      return 'Error';
    case 'debug':
      return 'Debug';
    default:
      return 'Unknown';
  }
};
