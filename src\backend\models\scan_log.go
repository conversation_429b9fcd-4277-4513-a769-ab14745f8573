package models

import (
	"time"
)

// LogLevelDebug is an additional log level for scan operations
const LogLevelDebug LogLevel = "debug"

// ScanStatus represents the status of a scan operation
type ScanStatus string

const (
	ScanStatusQueued    ScanStatus = "queued"
	ScanStatusRunning   ScanStatus = "running"
	ScanStatusCompleted ScanStatus = "completed"
	ScanStatusFailed    ScanStatus = "failed"
	ScanStatusCancelled ScanStatus = "cancelled"
)

// ScanLogEntry represents a single log entry during a scan operation
type ScanLogEntry struct {
	ID         string      `json:"id" bson:"_id,omitempty"`
	ScanID     string      `json:"scanId" bson:"scanId"`
	RepoID     string      `json:"repoId" bson:"repoId"`
	GroupName  string      `json:"groupName" bson:"groupName"`
	Timestamp  time.Time   `json:"timestamp" bson:"timestamp"`
	Level      LogLevel    `json:"level" bson:"level"`
	Message    string      `json:"message" bson:"message"`
	Details    string      `json:"details,omitempty" bson:"details,omitempty"`
	Step       string      `json:"step,omitempty" bson:"step,omitempty"`
	Progress   *Progress   `json:"progress,omitempty" bson:"progress,omitempty"`
	SourceID   string      `json:"sourceId,omitempty" bson:"sourceId,omitempty"`
	SourceName string      `json:"sourceName,omitempty" bson:"sourceName,omitempty"`
	Error      string      `json:"error,omitempty" bson:"error,omitempty"`
	Duration   *int64      `json:"duration,omitempty" bson:"duration,omitempty"` // Duration in milliseconds
	Metadata   interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// Progress represents the current progress of a scan operation
type Progress struct {
	Current     int    `json:"current" bson:"current"`
	Total       int    `json:"total" bson:"total"`
	Percentage  int    `json:"percentage" bson:"percentage"`
	Description string `json:"description,omitempty" bson:"description,omitempty"`

	// Enhanced progress tracking
	CurrentFile            string     `json:"currentFile,omitempty" bson:"currentFile,omitempty"`
	CurrentChunk           int        `json:"currentChunk,omitempty" bson:"currentChunk,omitempty"`
	TotalChunks            int        `json:"totalChunks,omitempty" bson:"totalChunks,omitempty"`
	ChunkSize              int        `json:"chunkSize,omitempty" bson:"chunkSize,omitempty"`
	FilesPerSecond         float64    `json:"filesPerSecond,omitempty" bson:"filesPerSecond,omitempty"`
	EstimatedTimeRemaining *int64     `json:"estimatedTimeRemaining,omitempty" bson:"estimatedTimeRemaining,omitempty"` // milliseconds
	StartTime              *time.Time `json:"startTime,omitempty" bson:"startTime,omitempty"`
	LastUpdateTime         *time.Time `json:"lastUpdateTime,omitempty" bson:"lastUpdateTime,omitempty"`
}

// ScanLogSummary represents a summary of a complete scan operation
type ScanLogSummary struct {
	ScanID           string              `json:"scanId" bson:"_id"`
	RepoID           string              `json:"repoId" bson:"repoId"`
	GroupName        string              `json:"groupName" bson:"groupName"`
	Status           ScanStatus          `json:"status" bson:"status"`
	StartTime        time.Time           `json:"startTime" bson:"startTime"`
	EndTime          *time.Time          `json:"endTime,omitempty" bson:"endTime,omitempty"`
	Duration         *int64              `json:"duration,omitempty" bson:"duration,omitempty"` // Duration in milliseconds
	TotalSteps       int                 `json:"totalSteps" bson:"totalSteps"`
	CompletedSteps   int                 `json:"completedSteps" bson:"completedSteps"`
	SourcesScanned   int                 `json:"sourcesScanned" bson:"sourcesScanned"`
	TotalSources     int                 `json:"totalSources" bson:"totalSources"`
	ResultsFound     int                 `json:"resultsFound" bson:"resultsFound"`
	ErrorCount       int                 `json:"errorCount" bson:"errorCount"`
	WarningCount     int                 `json:"warningCount" bson:"warningCount"`
	LastActivity     time.Time           `json:"lastActivity" bson:"lastActivity"`
	CancellationInfo *CancellationInfo   `json:"cancellationInfo,omitempty" bson:"cancellationInfo,omitempty"`
	Performance      *PerformanceMetrics `json:"performance,omitempty" bson:"performance,omitempty"`

	// Enhanced progress tracking
	CurrentProgress   *Progress          `json:"currentProgress,omitempty" bson:"currentProgress,omitempty"`
	TotalFiles        int                `json:"totalFiles,omitempty" bson:"totalFiles,omitempty"`
	ProcessedFiles    int                `json:"processedFiles,omitempty" bson:"processedFiles,omitempty"`
	ChunkedProcessing bool               `json:"chunkedProcessing,omitempty" bson:"chunkedProcessing,omitempty"`
	ChunkSize         int                `json:"chunkSize,omitempty" bson:"chunkSize,omitempty"`
	CurrentChunk      int                `json:"currentChunk,omitempty" bson:"currentChunk,omitempty"`
	TotalChunks       int                `json:"totalChunks,omitempty" bson:"totalChunks,omitempty"`
	SourceStatuses    []SourceScanStatus `json:"sourceStatuses,omitempty" bson:"sourceStatuses,omitempty"`
}

// CancellationInfo contains information about scan cancellation
type CancellationInfo struct {
	CancelledAt time.Time `json:"cancelledAt" bson:"cancelledAt"`
	Reason      string    `json:"reason,omitempty" bson:"reason,omitempty"`
	UserID      string    `json:"userId,omitempty" bson:"userId,omitempty"`
}

// PerformanceMetrics contains performance information about the scan
type PerformanceMetrics struct {
	AverageStepDuration int64    `json:"averageStepDuration" bson:"averageStepDuration"` // milliseconds
	TotalProcessingTime int64    `json:"totalProcessingTime" bson:"totalProcessingTime"` // milliseconds
	ItemsPerSecond      float64  `json:"itemsPerSecond" bson:"itemsPerSecond"`
	MemoryUsage         *int64   `json:"memoryUsage,omitempty" bson:"memoryUsage,omitempty"` // bytes
	CPUUsage            *float64 `json:"cpuUsage,omitempty" bson:"cpuUsage,omitempty"`       // percentage
}

// SourceScanStatus represents the status of scanning a specific usage source
type SourceScanStatus struct {
	SourceID    string     `json:"sourceId" bson:"sourceId"`
	SourceName  string     `json:"sourceName" bson:"sourceName"`
	SourceType  string     `json:"sourceType" bson:"sourceType"`
	Status      ScanStatus `json:"status" bson:"status"`
	StartTime   *time.Time `json:"startTime,omitempty" bson:"startTime,omitempty"`
	EndTime     *time.Time `json:"endTime,omitempty" bson:"endTime,omitempty"`
	Duration    *int64     `json:"duration,omitempty" bson:"duration,omitempty"` // milliseconds
	ResultCount int        `json:"resultCount" bson:"resultCount"`
	ErrorCount  int        `json:"errorCount" bson:"errorCount"`
	LastError   string     `json:"lastError,omitempty" bson:"lastError,omitempty"`
}

// ScanLogFilter represents filtering options for scan logs
type ScanLogFilter struct {
	RepoID      string     `json:"repoId,omitempty"`
	GroupName   string     `json:"groupName,omitempty"`
	ScanID      string     `json:"scanId,omitempty"`
	Level       LogLevel   `json:"level,omitempty"`
	Status      string     `json:"status,omitempty"`
	SourceID    string     `json:"sourceId,omitempty"`
	StartTime   *time.Time `json:"startTime,omitempty"`
	EndTime     *time.Time `json:"endTime,omitempty"`
	SearchQuery string     `json:"searchQuery,omitempty"`
	Page        int        `json:"page,omitempty"`
	PageSize    int        `json:"pageSize,omitempty"`
}

// ScanLogResponse represents the response for scan log queries
type ScanLogResponse struct {
	Logs       []ScanLogEntry  `json:"logs"`
	Summary    *ScanLogSummary `json:"summary,omitempty"`
	Total      int             `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"pageSize"`
	TotalPages int             `json:"totalPages"`
}

// ScanLogsOverview represents an overview of all scan activities
type ScanLogsOverview struct {
	TotalScans       int                 `json:"totalScans"`
	ActiveScans      int                 `json:"activeScans"`
	CompletedScans   int                 `json:"completedScans"`
	FailedScans      int                 `json:"failedScans"`
	RecentActivity   []ScanLogSummary    `json:"recentActivity"`
	TopErrors        []ErrorSummary      `json:"topErrors,omitempty"`
	PerformanceStats *OverallPerformance `json:"performanceStats,omitempty"`
}

// ErrorSummary represents a summary of common errors
type ErrorSummary struct {
	Error          string    `json:"error"`
	Count          int       `json:"count"`
	LastOccured    time.Time `json:"lastOccurred"`
	AffectedGroups []string  `json:"affectedGroups,omitempty"`
}

// OverallPerformance represents overall performance statistics
type OverallPerformance struct {
	AverageScanDuration   int64   `json:"averageScanDuration"` // milliseconds
	SuccessRate           float64 `json:"successRate"`         // percentage
	TotalResultsFound     int     `json:"totalResultsFound"`
	AverageResultsPerScan int     `json:"averageResultsPerScan"`
}

// ChunkedProcessingConfig represents configuration for chunked file processing
type ChunkedProcessingConfig struct {
	Enabled                bool `json:"enabled" bson:"enabled"`
	ChunkSize              int  `json:"chunkSize" bson:"chunkSize"`                           // Number of files per chunk
	MaxConcurrentChunks    int  `json:"maxConcurrentChunks" bson:"maxConcurrentChunks"`       // Max parallel chunks
	ProgressUpdateInterval int  `json:"progressUpdateInterval" bson:"progressUpdateInterval"` // Update frequency in files
}

// FileProcessingStatus represents the status of processing a specific file
type FileProcessingStatus struct {
	FilePath     string     `json:"filePath" bson:"filePath"`
	Status       string     `json:"status" bson:"status"` // "pending", "processing", "completed", "failed"
	StartTime    *time.Time `json:"startTime,omitempty" bson:"startTime,omitempty"`
	EndTime      *time.Time `json:"endTime,omitempty" bson:"endTime,omitempty"`
	Duration     *int64     `json:"duration,omitempty" bson:"duration,omitempty"` // milliseconds
	ResultsFound int        `json:"resultsFound" bson:"resultsFound"`
	Error        string     `json:"error,omitempty" bson:"error,omitempty"`
	ChunkIndex   int        `json:"chunkIndex" bson:"chunkIndex"`
}

// ChunkProcessingStatus represents the status of processing a chunk of files
type ChunkProcessingStatus struct {
	ChunkIndex     int                    `json:"chunkIndex" bson:"chunkIndex"`
	TotalFiles     int                    `json:"totalFiles" bson:"totalFiles"`
	ProcessedFiles int                    `json:"processedFiles" bson:"processedFiles"`
	Status         string                 `json:"status" bson:"status"` // "pending", "processing", "completed", "failed"
	StartTime      *time.Time             `json:"startTime,omitempty" bson:"startTime,omitempty"`
	EndTime        *time.Time             `json:"endTime,omitempty" bson:"endTime,omitempty"`
	Duration       *int64                 `json:"duration,omitempty" bson:"duration,omitempty"` // milliseconds
	ResultsFound   int                    `json:"resultsFound" bson:"resultsFound"`
	ErrorCount     int                    `json:"errorCount" bson:"errorCount"`
	Files          []FileProcessingStatus `json:"files,omitempty" bson:"files,omitempty"`
}

// RealTimeProgressUpdate represents a real-time progress update for WebSocket broadcasting
type RealTimeProgressUpdate struct {
	ScanID       string                 `json:"scanId"`
	RepoID       string                 `json:"repoId"`
	GroupName    string                 `json:"groupName"`
	Timestamp    time.Time              `json:"timestamp"`
	Progress     *Progress              `json:"progress,omitempty"`
	CurrentChunk *ChunkProcessingStatus `json:"currentChunk,omitempty"`
	Summary      *ScanLogSummary        `json:"summary,omitempty"`
	EventType    string                 `json:"eventType"` // "progress", "chunk_start", "chunk_complete", "file_complete", "scan_complete", "error"
	Message      string                 `json:"message,omitempty"`
	Error        string                 `json:"error,omitempty"`
}
