// Scheduler dashboard types

export interface ServiceStatus {
  name: string;
  isRunning: boolean;
  status: 'running' | 'stopped' | 'error';
  lastActivity: string;
  errorMessage?: string;
  activeTasks: number;
  queuedTasks: number;
}

export interface SchedulerStatus {
  reportScheduler: ServiceStatus;
  usageScanner: ServiceStatus;
  autoScanService: ServiceStatus;
  workerPool: ServiceStatus;
  overallHealth: 'healthy' | 'degraded' | 'unhealthy';
  lastUpdated: string;
}

export interface PlannedTask {
  id: string;
  type: 'report' | 'scan' | 'auto-scan';
  name: string;
  description: string;
  scheduledFor: string;
  repository?: string;
  groupName?: string;
  priority: 'high' | 'medium' | 'low';
  metadata?: Record<string, any>;
}

export interface Progress {
  current: number;
  total: number;
  percentage: number;
  description?: string;
}

export interface ActiveTask {
  id: string;
  type: 'report' | 'scan' | 'auto-scan';
  name: string;
  startedAt: string;
  progress?: Progress;
  repository?: string;
  groupName?: string;
  workerId?: string;
  status: 'running' | 'completing';
  metadata?: Record<string, any>;
}

export interface FailedTask {
  id: string;
  type: 'report' | 'scan' | 'auto-scan';
  name: string;
  failedAt: string;
  errorMessage: string;
  retryCount: number;
  canRetry: boolean;
  repository?: string;
  groupName?: string;
  lastAttempt?: string;
  metadata?: Record<string, any>;
}

export interface TaskControlRequest {
  action: 'pause' | 'resume' | 'trigger' | 'retry' | 'cancel';
  service: 'all' | 'report' | 'usage' | 'auto-scan' | 'worker-pool';
  taskId?: string;
  taskType?: string;
}

export interface TaskControlResponse {
  success: boolean;
  message: string;
  taskId?: string;
  totalCleaned?: number; // For cleanup operations
}

export interface SchedulerOverview {
  totalPlannedTasks: number;
  activeTasks: number;
  failedTasks: number;
  completedToday: number;
  successRate: number; // percentage
  averageTaskTime: number; // seconds
}

// API Response types
export interface PlannedWorkResponse {
  tasks: PlannedTask[];
  total: number;
}

export interface ActiveTasksResponse {
  tasks: ActiveTask[];
  total: number;
}

export interface FailedTasksResponse {
  tasks: FailedTask[];
  total: number;
}

// Interrupted Scans Management types
export interface InterruptedScansStats {
  interruptedCount: number;
  failedCount: number;
  pendingRestart: number;
}

export interface InterruptedScan {
  id: string;
  groupName: string;
  repository: string;
  status: 'interrupted' | 'failed';
  startedAt: string;
  progress: number;
  errorMessage?: string;
  canRestart: boolean;
  metadata?: Record<string, any>;
}

export interface InterruptedScansResponse {
  scans: InterruptedScan[];
  total: number;
  stats: InterruptedScansStats;
}

// UI Display types
export interface TaskFilter {
  type?: 'report' | 'scan' | 'auto-scan';
  status?: string;
  repository?: string;
  groupName?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface TaskDisplayOptions {
  showProgress: boolean;
  showMetadata: boolean;
  autoRefresh: boolean;
  refreshInterval: number; // seconds
  groupByType: boolean;
}

// Status display helpers
export const ServiceStatusColors: Record<ServiceStatus['status'], string> = {
  running: 'text-green-600 bg-green-50',
  stopped: 'text-yellow-600 bg-yellow-50',
  error: 'text-red-600 bg-red-50',
};

export const ServiceStatusIcons: Record<ServiceStatus['status'], string> = {
  running: 'check-circle',
  stopped: 'pause-circle',
  error: 'x-circle',
};

export const OverallHealthColors: Record<SchedulerStatus['overallHealth'], string> = {
  healthy: 'text-green-600 bg-green-50',
  degraded: 'text-yellow-600 bg-yellow-50',
  unhealthy: 'text-red-600 bg-red-50',
};

export const TaskTypeColors: Record<PlannedTask['type'], string> = {
  report: 'text-blue-600 bg-blue-50',
  scan: 'text-purple-600 bg-purple-50',
  'auto-scan': 'text-indigo-600 bg-indigo-50',
};

export const TaskPriorityColors: Record<PlannedTask['priority'], string> = {
  high: 'text-red-600 bg-red-50',
  medium: 'text-yellow-600 bg-yellow-50',
  low: 'text-green-600 bg-green-50',
};

// Helper functions
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

export const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};

export const formatRelativeTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();

  if (diffMs < 60000) { // Less than 1 minute
    return 'Just now';
  }

  if (diffMs < 3600000) { // Less than 1 hour
    const minutes = Math.floor(diffMs / 60000);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  }

  if (diffMs < 86400000) { // Less than 1 day
    const hours = Math.floor(diffMs / 3600000);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  }

  const days = Math.floor(diffMs / 86400000);
  return `${days} day${days > 1 ? 's' : ''} ago`;
};

export const getProgressPercentage = (progress?: Progress): number => {
  if (!progress || progress.total === 0) return 0;
  return Math.round((progress.current / progress.total) * 100);
};

export const getTaskTypeDisplayName = (type: PlannedTask['type']): string => {
  switch (type) {
    case 'report':
      return 'Report Generation';
    case 'scan':
      return 'Usage Scan';
    case 'auto-scan':
      return 'Auto Scan';
    default:
      return 'Unknown';
  }
};

export const getServiceDisplayName = (serviceName: string): string => {
  switch (serviceName) {
    case 'Report Scheduler':
      return 'Report Scheduler';
    case 'Usage Scanner':
      return 'Usage Scanner';
    case 'Auto Scan Service':
      return 'Auto Scan Service';
    case 'Worker Pool':
      return 'Worker Pool';
    default:
      return serviceName;
  }
};

export const calculateTimeUntilScheduled = (scheduledFor: string): string => {
  const scheduled = new Date(scheduledFor);
  const now = new Date();
  const diffMs = scheduled.getTime() - now.getTime();

  if (diffMs <= 0) {
    return 'Overdue';
  }

  if (diffMs < 60000) { // Less than 1 minute
    return 'Starting soon';
  }

  if (diffMs < 3600000) { // Less than 1 hour
    const minutes = Math.floor(diffMs / 60000);
    return `In ${minutes} minute${minutes > 1 ? 's' : ''}`;
  }

  if (diffMs < 86400000) { // Less than 1 day
    const hours = Math.floor(diffMs / 3600000);
    return `In ${hours} hour${hours > 1 ? 's' : ''}`;
  }

  const days = Math.floor(diffMs / 86400000);
  return `In ${days} day${days > 1 ? 's' : ''}`;
};
