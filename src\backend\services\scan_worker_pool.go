package services

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"
)

// ScanWorkerPool manages a pool of worker goroutines
type ScanWorkerPool struct {
	workers        int
	jobQueue       chan ScanJob
	resultsChannel chan <PERSON><PERSON><PERSON><PERSON><PERSON>
	done           chan struct{}
	wg             sync.WaitGroup
	isRunning      bool
	mutex          sync.Mutex
	statusManager  *ScanStatusManager
	scanLogger     *ScanLogger

	// Scan tracking
	activeScanSources map[string]int // scanID -> remaining source count
	scanTrackingMutex sync.Mutex

	// Performance optimizations
	batchSize    int           // Number of results to batch before processing
	batchTimeout time.Duration // Maximum time to wait for batch completion
}

// NewScanWorkerPool creates a new scan worker pool
func NewScanWorkerPool(workers int) *ScanWorkerPool {
	return &ScanWorkerPool{
		workers:           workers,
		jobQueue:          make(chan <PERSON>an<PERSON><PERSON>, workers*2), // Buffer for 2x workers
		resultsChannel:    make(chan <PERSON><PERSON><PERSON><PERSON><PERSON>, workers*2),
		done:              make(chan struct{}),
		isRunning:         false,
		activeScanSources: make(map[string]int),
		batchSize:         10,              // Process results in batches of 10
		batchTimeout:      5 * time.Second, // Maximum 5 seconds between batches
	}
}

// SetStatusManager sets the status manager for the worker pool
func (p *ScanWorkerPool) SetStatusManager(statusManager *ScanStatusManager) {
	p.statusManager = statusManager
}

// SetScanLogger sets the scan logger for the worker pool
func (p *ScanWorkerPool) SetScanLogger(scanLogger *ScanLogger) {
	p.scanLogger = scanLogger
}

// RegisterScanSources registers the number of sources for a scan
func (p *ScanWorkerPool) RegisterScanSources(scanID string, sourceCount int) {
	p.scanTrackingMutex.Lock()
	defer p.scanTrackingMutex.Unlock()
	p.activeScanSources[scanID] = sourceCount
}

// checkScanCompletion checks if a scan is complete and logs completion
func (p *ScanWorkerPool) checkScanCompletion(scanID string) {
	p.scanTrackingMutex.Lock()
	defer p.scanTrackingMutex.Unlock()

	if remaining, exists := p.activeScanSources[scanID]; exists {
		remaining--
		if remaining <= 0 {
			// Scan is complete
			delete(p.activeScanSources, scanID)
			if p.scanLogger != nil {
				p.scanLogger.CompleteScan(scanID, models.ScanStatusCompleted)
			}

			// Remove heartbeat for completed scan
			if p.statusManager != nil {
				// Extract groupName and repoID from the first result we can find
				// This is a bit of a hack, but we need these values to remove the heartbeat
				// In a production system, we'd want to track this mapping more explicitly
				log.Printf("Scan %s completed - heartbeat cleanup will be handled by scan completion", scanID)
			}

			log.Printf("Scan %s completed", scanID)
		} else {
			p.activeScanSources[scanID] = remaining
		}
	}
}

// Start starts the worker pool
func (p *ScanWorkerPool) Start() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.isRunning {
		return
	}

	p.isRunning = true

	// Start worker goroutines
	for i := 0; i < p.workers; i++ {
		p.wg.Add(1)
		go p.worker(i)
	}

	// Start results processor
	p.wg.Add(1)
	go p.processResults()

	log.Printf("Started scan worker pool with %d workers", p.workers)
}

// Stop stops the worker pool
func (p *ScanWorkerPool) Stop() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.isRunning {
		return
	}

	p.isRunning = false

	// Signal all goroutines to stop
	close(p.done)

	// Close job queue to signal workers to stop
	close(p.jobQueue)

	// Wait for all workers to finish
	p.wg.Wait()

	// Now it's safe to close results channel since all workers are done
	close(p.resultsChannel)

	log.Println("Stopped scan worker pool")
}

// SubmitJob submits a job to the pool
func (p *ScanWorkerPool) SubmitJob(job ScanJob) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.isRunning {
		return fmt.Errorf("worker pool is not running")
	}

	select {
	case p.jobQueue <- job:
		log.Printf("Submitted scan job for group %s, source %s", job.GroupName, job.Source.Name)
		return nil
	default:
		return fmt.Errorf("job queue is full")
	}
}

// worker is the worker goroutine
func (p *ScanWorkerPool) worker(id int) {
	defer p.wg.Done()

	log.Printf("Worker %d started", id)

	for job := range p.jobQueue {
		log.Printf("Worker %d processing job for group %s, source %s", id, job.GroupName, job.Source.Name)

		startTime := time.Now()
		result := p.processJob(job)
		result.CompletedAt = time.Now()

		log.Printf("Worker %d completed job for group %s, source %s in %v",
			id, job.GroupName, job.Source.Name, result.CompletedAt.Sub(startTime))

		// Send result to results channel (check if channel is still open)
		select {
		case p.resultsChannel <- result:
		case <-job.Context.Done():
			log.Printf("Worker %d: context cancelled while sending result", id)
		default:
			// Channel might be closed, check if pool is still running
			p.mutex.Lock()
			if p.isRunning {
				// Pool is running but channel is full, try again with timeout
				select {
				case p.resultsChannel <- result:
				case <-time.After(100 * time.Millisecond):
					log.Printf("Worker %d: timeout sending result", id)
				}
			}
			p.mutex.Unlock()
		}
	}

	log.Printf("Worker %d stopped", id)
}

// processJob processes a single scan job
func (p *ScanWorkerPool) processJob(job ScanJob) ScanResult {
	startTime := time.Now()
	result := ScanResult{
		GroupName: job.GroupName,
		RepoID:    job.RepoID,
		SourceID:  job.Source.ID,
		ScanID:    job.ScanID,
		Results:   []models.UsageResult{},
		Error:     nil,
	}

	// Log scan start with detailed information
	log.Printf("Starting scan job for source '%s' (ID: %s, Type: %s) in group '%s'",
		job.Source.Name, job.Source.ID, job.Source.Type, job.GroupName)

	// Set up scan logger on the handler if it supports it
	if gitHandler, ok := job.Handler.(*GitSourceHandler); ok && job.Logger != nil {
		gitHandler.SetScanLogger(job.Logger, job.ScanID)
	}

	// Get timeout values from source configuration (with defaults)
	connectionTimeout := time.Duration(job.Source.ConnectionTimeout) * time.Second
	if connectionTimeout == 0 {
		connectionTimeout = 60 * time.Second // Default 1 minute for connection
	}

	scanTimeout := time.Duration(job.Source.ScanTimeout) * time.Second
	if scanTimeout == 0 {
		scanTimeout = 600 * time.Second // Default 10 minutes for full scan
	}

	// Add timeout context for the entire scan operation
	ctx, cancel := context.WithTimeout(job.Context, scanTimeout)
	defer cancel()

	log.Printf("Using timeouts for source %s: connection=%v, scan=%v",
		job.Source.Name, connectionTimeout, scanTimeout)

	// Check if context is already cancelled
	select {
	case <-ctx.Done():
		result.Error = ctx.Err()
		result.CompletedAt = time.Now()
		log.Printf("Scan job cancelled/timed out for source %s after %v", job.Source.Name, time.Since(startTime))
		return result
	default:
	}

	// Test connection first with connection timeout
	log.Printf("Testing connection for source %s...", job.Source.Name)
	connCtx, connCancel := context.WithTimeout(ctx, connectionTimeout)
	defer connCancel()

	// Use a channel to handle timeout properly
	connDone := make(chan error, 1)
	go func() {
		connDone <- job.Handler.TestConnection(connCtx)
	}()

	select {
	case err := <-connDone:
		if err != nil {
			log.Printf("Connection test failed for source %s after %v: %v", job.Source.Name, time.Since(startTime), err)
			result.Error = fmt.Errorf("connection test failed: %w", err)
			result.CompletedAt = time.Now()
			return result
		}
		log.Printf("Connection test successful for source %s", job.Source.Name)
	case <-connCtx.Done():
		log.Printf("Connection test timed out for source %s after %v", job.Source.Name, connectionTimeout)
		result.Error = fmt.Errorf("connection test timed out after %v", connectionTimeout)
		result.CompletedAt = time.Now()
		return result
	}

	// Perform the actual scan with timeout handling
	log.Printf("Starting group usage scan for source %s...", job.Source.Name)

	// Use a channel to handle scan timeout properly
	scanDone := make(chan struct {
		results []models.UsageResult
		err     error
	}, 1)

	go func() {
		results, err := job.Handler.ScanForGroupUsage(ctx, job.GroupName)
		scanDone <- struct {
			results []models.UsageResult
			err     error
		}{results, err}
	}()

	select {
	case scanResult := <-scanDone:
		if scanResult.err != nil {
			log.Printf("Scan failed for source %s after %v: %v", job.Source.Name, time.Since(startTime), scanResult.err)
			result.Error = fmt.Errorf("scan failed: %w", scanResult.err)
			result.CompletedAt = time.Now()
			return result
		}
		usageResults := scanResult.results

		result.Results = usageResults
		result.CompletedAt = time.Now()
		duration := time.Since(startTime)
		log.Printf("Scan completed for source %s: found %d usage results for group %s in %v",
			job.Source.Name, len(usageResults), job.GroupName, duration)

		return result
	case <-ctx.Done():
		log.Printf("Scan timed out for source %s after %v", job.Source.Name, time.Since(startTime))
		result.Error = fmt.Errorf("scan timed out after %v", time.Since(startTime))
		result.CompletedAt = time.Now()
		return result
	}
}

// processResults processes scan results with batching for better performance
func (p *ScanWorkerPool) processResults() {
	defer p.wg.Done()

	log.Println("Results processor started")

	var batch []ScanResult
	batchTimer := time.NewTimer(p.batchTimeout)
	defer batchTimer.Stop()

	processBatch := func() {
		if len(batch) == 0 {
			return
		}

		log.Printf("Processing batch of %d results", len(batch))

		for _, result := range batch {
			// Log scan result to scan logger
			if p.scanLogger != nil && result.ScanID != "" {
				if result.Error != nil {
					// Log error
					p.scanLogger.LogError(result.ScanID, "source_scan",
						fmt.Sprintf("Scan failed for source %s", result.SourceID),
						result.Error.Error(), result.SourceID)
				} else {
					// Calculate scan duration (approximate)
					scanDuration := time.Since(result.CompletedAt.Add(-time.Minute)) // Rough estimate

					// Log successful completion
					p.scanLogger.LogSourceComplete(result.ScanID, result.SourceID,
						len(result.Results), scanDuration)

					// Log individual results if any found
					if len(result.Results) > 0 {
						p.scanLogger.LogStep(result.ScanID, "results_found",
							fmt.Sprintf("Found %d usage results in source %s", len(result.Results), result.SourceID),
							nil)
					}
				}
			}

			// Update heartbeat for active scan
			if p.statusManager != nil && result.GroupName != "" && result.RepoID != "" {
				p.statusManager.UpdateHeartbeat(result.GroupName, result.RepoID)
			}

			// Log scan result if scan logger is available
			if p.scanLogger != nil && result.ScanID != "" {
				if result.Error != nil {
					// Log error
					p.scanLogger.LogError(result.ScanID, "source_scan",
						fmt.Sprintf("Scan failed for source %s", result.SourceID),
						result.Error.Error())
				} else {
					// Log successful completion
					duration := time.Since(result.CompletedAt)
					if result.CompletedAt.IsZero() {
						duration = 0 // If CompletedAt is not set, use 0 duration
					}
					p.scanLogger.LogSourceComplete(result.ScanID, result.SourceID, len(result.Results), duration)

					// Log results found if any
					if len(result.Results) > 0 {
						p.scanLogger.LogStep(result.ScanID, "results_found",
							fmt.Sprintf("Found %d usage results in source %s", len(result.Results), result.SourceID),
							&models.Progress{
								Current:     len(result.Results),
								Total:       len(result.Results),
								Percentage:  100,
								Description: fmt.Sprintf("Results found in source %s", result.SourceID),
							})
					}
				}
			}

			if p.statusManager != nil {
				if result.Error != nil {
					// Handle failed scan
					log.Printf("Scan failed for group %s, source %s: %v", result.GroupName, result.SourceID, result.Error)

					// Update status with failure information
					if err := p.statusManager.UpdateSourceFailure(result.GroupName, result.RepoID, result.SourceID, result.Error.Error()); err != nil {
						log.Printf("Failed to update scan status with failure: %v", err)
					}
				} else {
					// Handle successful scan
					if err := p.statusManager.AddResults(result.GroupName, result.RepoID, result.SourceID, result.Results); err != nil {
						log.Printf("Failed to save scan results: %v", err)
					} else {
						log.Printf("Saved %d usage results for group %s, source %s", len(result.Results), result.GroupName, result.SourceID)
					}
				}
			} else {
				log.Printf("Warning: No status manager configured, results not saved")
			}

			// Check if this scan is complete
			if result.ScanID != "" {
				p.checkScanCompletion(result.ScanID)
			}
		}

		// Clear batch
		batch = batch[:0]
		batchTimer.Reset(p.batchTimeout)
	}

	for {
		select {
		case <-p.done:
			// Process any remaining results in batch before stopping
			processBatch()
			log.Println("Results processor stopped")
			return
		case result, ok := <-p.resultsChannel:
			if !ok {
				// Process any remaining results in batch before stopping
				processBatch()
				log.Println("Results processor stopped")
				return
			}

			// Add result to batch
			batch = append(batch, result)

			// Process batch if it's full
			if len(batch) >= p.batchSize {
				processBatch()
			}
		case <-batchTimer.C:
			// Process batch on timeout
			processBatch()
		}
	}
}

// GetQueueSize returns the current size of the job queue
func (p *ScanWorkerPool) GetQueueSize() int {
	return len(p.jobQueue)
}

// IsRunning returns whether the worker pool is running
func (p *ScanWorkerPool) IsRunning() bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	return p.isRunning
}
