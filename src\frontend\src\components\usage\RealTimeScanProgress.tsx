import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { EnhancedProgress, CompactProgress } from '@/components/ui/enhanced-progress';
import { useWebSocketProgress } from '@/hooks/useWebSocketProgress';
import {
  Wifi,
  WifiOff,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Activity
} from 'lucide-react';
import { RealTimeProgressUpdate, Progress, ChunkProcessingStatus } from '@/types/scanLogs';

interface RealTimeScanProgressProps {
  scanId: string;
  repoId: string;
  groupName: string;
  onScanComplete?: (results: any) => void;
  onScanError?: (error: string) => void;
  compact?: boolean;
  autoSubscribe?: boolean;
}

export function RealTimeScanProgress({
  scanId,
  repoId,
  groupName,
  onScanComplete,
  onScanError,
  compact = false,
  autoSubscribe = true
}: RealTimeScanProgressProps) {
  const {
    isConnected,
    isConnecting,
    error: wsError,
    lastUpdate,
    connect,
    disconnect,
    subscribeToScan,
    unsubscribeFromScan,
    getLatestUpdateForScan
  } = useWebSocketProgress();

  const [currentProgress, setCurrentProgress] = useState<Progress | null>(null);
  const [currentChunk, setCurrentChunk] = useState<ChunkProcessingStatus | null>(null);
  const [scanStatus, setScanStatus] = useState<'idle' | 'running' | 'completed' | 'failed'>('idle');
  const [lastMessage, setLastMessage] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Subscribe to scan updates when connected
  useEffect(() => {
    if (isConnected && autoSubscribe && scanId) {
      subscribeToScan(scanId);
      setScanStatus('running');
    }

    return () => {
      if (scanId) {
        unsubscribeFromScan(scanId);
      }
    };
  }, [isConnected, autoSubscribe, scanId, subscribeToScan, unsubscribeFromScan]);

  // Handle progress updates
  useEffect(() => {
    if (!lastUpdate || lastUpdate.scanId !== scanId) {
      return;
    }

    const update = lastUpdate;

    // Update progress data
    if (update.progress) {
      setCurrentProgress(update.progress);
    }

    if (update.currentChunk) {
      setCurrentChunk(update.currentChunk);
    }

    // Update status and messages
    setLastMessage(update.message || '');

    switch (update.eventType) {
      case 'scan_start':
        setScanStatus('running');
        setErrorMessage('');
        break;

      case 'scan_complete':
        setScanStatus('completed');
        if (onScanComplete) {
          onScanComplete(update.summary);
        }
        break;

      case 'scan_error':
        setScanStatus('failed');
        const error = update.error || 'Unknown error occurred';
        setErrorMessage(error);
        if (onScanError) {
          onScanError(error);
        }
        break;

      case 'progress':
      case 'file_progress':
      case 'chunk_start':
      case 'chunk_complete':
        setScanStatus('running');
        break;
    }
  }, [lastUpdate, scanId, onScanComplete, onScanError]);

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2">
      {isConnecting ? (
        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      ) : isConnected ? (
        <Wifi className="h-4 w-4 text-green-500" />
      ) : (
        <WifiOff className="h-4 w-4 text-red-500" />
      )}

      <span className="text-sm text-muted-foreground">
        {isConnecting ? 'Connecting...' : isConnected ? 'Connected' : 'Disconnected'}
      </span>

      {!isConnected && !isConnecting && (
        <Button
          size="sm"
          variant="outline"
          onClick={connect}
          className="h-6 px-2"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      )}
    </div>
  );

  // Status badge
  const StatusBadge = () => {
    const statusConfig = {
      idle: { icon: Activity, color: 'bg-gray-500', text: 'Idle' },
      running: { icon: Loader2, color: 'bg-blue-500', text: 'Running' },
      completed: { icon: CheckCircle, color: 'bg-green-500', text: 'Completed' },
      failed: { icon: XCircle, color: 'bg-red-500', text: 'Failed' }
    };

    const config = statusConfig[scanStatus];
    const Icon = config.icon;

    return (
      <Badge variant="outline" className="gap-1">
        <Icon className={`h-3 w-3 ${scanStatus === 'running' ? 'animate-spin' : ''}`} />
        {config.text}
      </Badge>
    );
  };

  // Compact version
  if (compact) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base">Scan Progress</CardTitle>
            <div className="flex items-center gap-2">
              <StatusBadge />
              <ConnectionStatus />
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {currentProgress ? (
            <CompactProgress progress={currentProgress} />
          ) : (
            <div className="text-center text-muted-foreground py-4">
              {scanStatus === 'idle' ? 'Waiting for scan to start...' : 'Loading progress...'}
            </div>
          )}

          {lastMessage && (
            <p className="text-xs text-muted-foreground mt-2 truncate" title={lastMessage}>
              {lastMessage}
            </p>
          )}
        </CardContent>
      </Card>
    );
  }

  // Full version
  return (
    <div className="space-y-4">
      {/* Connection and Status Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Real-time Scan Progress</CardTitle>
            <div className="flex items-center gap-3">
              <StatusBadge />
              <ConnectionStatus />
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            Scanning group "{groupName}" in repository "{repoId}"
          </div>
        </CardHeader>
      </Card>

      {/* Error Alert */}
      {(wsError || errorMessage) && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {errorMessage || wsError}
          </AlertDescription>
        </Alert>
      )}

      {/* Progress Display */}
      {currentProgress ? (
        <EnhancedProgress
          progress={currentProgress}
          currentChunk={currentChunk || undefined}
          title="File Processing Progress"
          showDetails={true}
        />
      ) : (
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-muted-foreground">
              {scanStatus === 'idle' ? (
                <div className="space-y-2">
                  <Activity className="h-8 w-8 mx-auto text-gray-400" />
                  <p>Waiting for scan to start...</p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Loader2 className="h-8 w-8 mx-auto animate-spin text-blue-500" />
                  <p>Initializing scan progress...</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Last Message */}
      {lastMessage && (
        <Card>
          <CardContent className="py-3">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-500" />
              <span className="text-sm">{lastMessage}</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
