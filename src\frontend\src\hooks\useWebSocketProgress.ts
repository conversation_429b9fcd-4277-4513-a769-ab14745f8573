import { useState, useEffect, useRef, useCallback } from 'react';
import { RealTimeProgressUpdate } from '@/types/scanLogs';

interface UseWebSocketProgressOptions {
  url?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
}

interface WebSocketProgressState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastUpdate: RealTimeProgressUpdate | null;
  updates: RealTimeProgressUpdate[];
}

export function useWebSocketProgress(options: UseWebSocketProgressOptions = {}) {
  // Determine WebSocket protocol based on current page protocol
  const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const defaultUrl = `${wsProtocol}//${window.location.host}/ws/progress`;

  const {
    url = defaultUrl,
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectInterval = 3000
  } = options;

  const [state, setState] = useState<WebSocketProgressState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastUpdate: null,
    updates: []
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);
  const subscribedScansRef = useRef<Set<string>>(new Set());

  // Clear reconnect timeout
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const clientId = `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const wsUrl = `${url}?clientId=${clientId}`;

      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected for progress updates');
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null
        }));
        reconnectCountRef.current = 0;

        // Re-subscribe to any previously subscribed scans
        subscribedScansRef.current.forEach(scanId => {
          subscribeToScan(scanId);
        });
      };

      wsRef.current.onmessage = (event) => {
        try {
          const update: RealTimeProgressUpdate = JSON.parse(event.data);

          setState(prev => ({
            ...prev,
            lastUpdate: update,
            updates: [...prev.updates.slice(-99), update] // Keep last 100 updates
          }));
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false
        }));

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++;
          console.log(`Attempting to reconnect (${reconnectCountRef.current}/${reconnectAttempts})...`);

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setState(prev => ({
          ...prev,
          error: 'WebSocket connection error',
          isConnecting: false
        }));
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to create WebSocket connection',
        isConnecting: false
      }));
    }
  }, [url, reconnectAttempts, reconnectInterval]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    clearReconnectTimeout();

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false
    }));
  }, [clearReconnectTimeout]);

  // Subscribe to scan updates
  const subscribeToScan = useCallback((scanId: string) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'subscribe_scan',
        scanId: scanId
      };
      wsRef.current.send(JSON.stringify(message));
      subscribedScansRef.current.add(scanId);
      console.log(`Subscribed to scan updates for: ${scanId}`);
    }
  }, []);

  // Unsubscribe from scan updates
  const unsubscribeFromScan = useCallback((scanId: string) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'unsubscribe_scan',
        scanId: scanId
      };
      wsRef.current.send(JSON.stringify(message));
      subscribedScansRef.current.delete(scanId);
      console.log(`Unsubscribed from scan updates for: ${scanId}`);
    }
  }, []);

  // Clear all updates
  const clearUpdates = useCallback(() => {
    setState(prev => ({
      ...prev,
      updates: [],
      lastUpdate: null
    }));
  }, []);

  // Get updates for a specific scan
  const getUpdatesForScan = useCallback((scanId: string): RealTimeProgressUpdate[] => {
    return state.updates.filter(update => update.scanId === scanId);
  }, [state.updates]);

  // Get latest update for a specific scan
  const getLatestUpdateForScan = useCallback((scanId: string): RealTimeProgressUpdate | null => {
    const scanUpdates = getUpdatesForScan(scanId);
    return scanUpdates.length > 0 ? scanUpdates[scanUpdates.length - 1] : null;
  }, [getUpdatesForScan]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      clearReconnectTimeout();
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmount');
      }
    };
  }, [autoConnect, connect, clearReconnectTimeout]);

  return {
    // Connection state
    isConnected: state.isConnected,
    isConnecting: state.isConnecting,
    error: state.error,

    // Progress data
    lastUpdate: state.lastUpdate,
    updates: state.updates,

    // Connection control
    connect,
    disconnect,

    // Subscription control
    subscribeToScan,
    unsubscribeFromScan,

    // Data utilities
    clearUpdates,
    getUpdatesForScan,
    getLatestUpdateForScan,

    // Connection info
    reconnectCount: reconnectCountRef.current,
    subscribedScans: Array.from(subscribedScansRef.current)
  };
}
