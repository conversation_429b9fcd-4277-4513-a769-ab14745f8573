package controllers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strings"
	"time"

	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"

	"github.com/gin-gonic/gin"
)

// SchedulerController handles scheduler monitoring and management
type SchedulerController struct {
	schedulerService *services.SchedulerService
	usageScanner     services.UsageScanner
	autoScanService  *services.AutoScanService
	workerPool       *services.ScanWorkerPool
	dataProcessor    services.DataProcessorInterface
}

// NewSchedulerController creates a new scheduler controller
func NewSchedulerController(
	schedulerService *services.SchedulerService,
	usageScanner services.UsageScanner,
	autoScanService *services.AutoScanService,
	workerPool *services.ScanWorkerPool,
	dataProcessor services.DataProcessorInterface,
) *SchedulerController {
	return &SchedulerController{
		schedulerService: schedulerService,
		usageScanner:     usageScanner,
		autoScanService:  autoScanService,
		workerPool:       workerPool,
		dataProcessor:    dataProcessor,
	}
}

// RegisterRoutes registers the scheduler controller routes
func (c *SchedulerController) RegisterRoutes(router *gin.RouterGroup) {
	schedulerRoutes := router.Group("/scheduler")
	{
		schedulerRoutes.GET("/status", c.GetSchedulerStatus)
		schedulerRoutes.GET("/overview", c.GetSchedulerOverview)
		schedulerRoutes.GET("/planned-work", c.GetPlannedWork)
		schedulerRoutes.GET("/active-tasks", c.GetActiveTasks)
		schedulerRoutes.GET("/failed-tasks", c.GetFailedTasks)

		// Interrupted scans management endpoints
		schedulerRoutes.GET("/interrupted-scans", c.GetInterruptedScans)
		schedulerRoutes.GET("/interrupted-scans/stats", c.GetInterruptedScansStats)

		// Task control endpoints
		controlRoutes := schedulerRoutes.Group("/control")
		{
			controlRoutes.POST("/pause", c.PauseScheduler)
			controlRoutes.POST("/resume", c.ResumeScheduler)
			controlRoutes.POST("/trigger", c.TriggerTask)
			controlRoutes.POST("/retry", c.RetryTask)
			controlRoutes.POST("/cancel", c.CancelTask)

			// Interrupted scans management
			controlRoutes.POST("/restart-interrupted", c.RestartInterruptedScans)
			controlRoutes.POST("/clear-failed", c.ClearFailedScans)
			controlRoutes.POST("/restart-scan", c.RestartSpecificScan)
			controlRoutes.DELETE("/remove-scan", c.RemoveSpecificScan)
			controlRoutes.POST("/cleanup-stale", c.CleanupStaleTasks)
		}
	}
}

// CleanupStaleTasks manually cleans up stale/orphaned tasks
func (c *SchedulerController) CleanupStaleTasks(ctx *gin.Context) {
	log.Println("Manual stale task cleanup requested")

	var totalCleaned int
	var errors []string

	// Clean up stale scan tasks
	if c.usageScanner != nil {
		cleaned, err := c.usageScanner.CleanupStaleTasks()
		if err != nil {
			log.Printf("Error cleaning up stale scan tasks: %v", err)
			errors = append(errors, fmt.Sprintf("Scan tasks: %v", err))
		} else {
			totalCleaned += cleaned
			log.Printf("Cleaned up %d stale scan tasks", cleaned)
		}
	}

	// TODO: Add cleanup for other task types (reports, auto-scans) when implemented

	response := gin.H{
		"success":      len(errors) == 0,
		"totalCleaned": totalCleaned,
		"message":      fmt.Sprintf("Cleaned up %d stale tasks", totalCleaned),
		"timestamp":    time.Now().Format(time.RFC3339),
	}

	if len(errors) > 0 {
		response["errors"] = errors
		response["message"] = fmt.Sprintf("Partially completed: cleaned %d tasks with %d errors", totalCleaned, len(errors))
	}

	ctx.JSON(http.StatusOK, response)
}

// CancelTask cancels a running task
func (c *SchedulerController) CancelTask(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	if request.TaskID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	log.Printf("Cancelling task: %s (type: %s)", request.TaskID, request.TaskType)

	var err error
	var message string

	// Route cancellation request based on task type or ID pattern
	switch {
	case request.TaskType == "usage_scan" || strings.Contains(request.TaskID, ":"):
		// Usage scan tasks have format "groupName:repoID"
		parts := strings.Split(request.TaskID, ":")
		if len(parts) != 2 {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid usage scan task ID format. Expected 'groupName:repoID'"})
			return
		}
		groupName, repoID := parts[0], parts[1]
		err = c.usageScanner.CancelScan(groupName, repoID)
		if err == nil {
			message = fmt.Sprintf("Successfully cancelled usage scan for group '%s' in repository '%s'", groupName, repoID)
		}

	case request.TaskType == "report" || request.TaskType == "scheduled_report":
		// TODO: Implement report task cancellation when report service supports it
		err = fmt.Errorf("report task cancellation not yet implemented")

	case request.TaskType == "auto_scan":
		// TODO: Implement auto scan task cancellation when auto scan service supports it
		err = fmt.Errorf("auto scan task cancellation not yet implemented")

	default:
		// Try to infer task type from ID or attempt generic cancellation
		if strings.Contains(request.TaskID, ":") {
			// Assume it's a usage scan
			parts := strings.Split(request.TaskID, ":")
			if len(parts) == 2 {
				groupName, repoID := parts[0], parts[1]
				err = c.usageScanner.CancelScan(groupName, repoID)
				if err == nil {
					message = fmt.Sprintf("Successfully cancelled usage scan for group '%s' in repository '%s'", groupName, repoID)
				}
			} else {
				err = fmt.Errorf("unknown task ID format: %s", request.TaskID)
			}
		} else {
			err = fmt.Errorf("unknown task type '%s' or unable to determine task type from ID '%s'", request.TaskType, request.TaskID)
		}
	}

	// Prepare response
	response := models.TaskControlResponse{
		TaskID: request.TaskID,
	}

	if err != nil {
		response.Success = false
		response.Message = fmt.Sprintf("Failed to cancel task: %s", err.Error())
		log.Printf("Failed to cancel task %s: %v", request.TaskID, err)

		// Return appropriate HTTP status based on error type
		errorMsg := err.Error()
		switch {
		case strings.Contains(errorMsg, "not found") || strings.Contains(errorMsg, "no scan in progress"):
			ctx.JSON(http.StatusNotFound, response)
		case strings.Contains(errorMsg, "already cancelled"):
			response.Message = fmt.Sprintf("Task %s has already been cancelled", request.TaskID)
			ctx.JSON(http.StatusConflict, response)
		case strings.Contains(errorMsg, "not yet implemented"):
			ctx.JSON(http.StatusNotImplemented, response)
		case strings.Contains(errorMsg, "invalid") || strings.Contains(errorMsg, "format"):
			ctx.JSON(http.StatusBadRequest, response)
		default:
			ctx.JSON(http.StatusInternalServerError, response)
		}
		return
	}

	response.Success = true
	response.Message = message
	log.Printf("Successfully cancelled task: %s", request.TaskID)
	ctx.JSON(http.StatusOK, response)
}

// RemoveSpecificScan removes a specific scan and its data by ID
func (c *SchedulerController) RemoveSpecificScan(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Parse task ID to extract groupName and repoID
	// Expected format: "groupName:repoID"
	parts := strings.Split(request.TaskID, ":")
	if len(parts) != 2 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid task ID format. Expected format: 'groupName:repoID'",
		})
		return
	}

	groupName := parts[0]
	repoID := parts[1]

	log.Printf("Remove specific scan request received for group: %s, repo: %s", groupName, repoID)

	// Check if scan exists
	currentStatus, err := c.usageScanner.GetScanStatus(groupName, repoID)
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Scan not found for group '%s' in repository '%s'", groupName, repoID),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusNotFound, response)
		return
	}

	// If scan is currently in progress, cancel it first
	if currentStatus.InProgress {
		log.Printf("Canceling current scan for group %s in repo %s before removal", groupName, repoID)
		if err := c.usageScanner.CancelScan(groupName, repoID); err != nil {
			log.Printf("Warning: Failed to cancel current scan: %v", err)
		}
		// Wait a moment for cancellation to take effect
		time.Sleep(1 * time.Second)
	}

	// Clear/remove the scan data
	if err := c.usageScanner.ClearUsageResults(groupName, repoID); err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to remove scan data: %v", err),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	response := models.TaskControlResponse{
		Success: true,
		Message: fmt.Sprintf("Successfully removed scan for group '%s' in repository '%s'", groupName, repoID),
		TaskID:  request.TaskID,
	}

	log.Printf("Successfully removed scan for group %s in repo %s", groupName, repoID)
	ctx.JSON(http.StatusOK, response)
}

// GetSchedulerStatus returns the overall status of all scheduler services
func (c *SchedulerController) GetSchedulerStatus(ctx *gin.Context) {
	status := models.SchedulerStatus{
		ReportScheduler: c.getReportSchedulerStatus(),
		UsageScanner:    c.getUsageScannerStatus(),
		AutoScanService: c.getAutoScanServiceStatus(),
		WorkerPool:      c.getWorkerPoolStatus(),
		LastUpdated:     time.Now().Format(time.RFC3339),
	}

	// Determine overall health
	status.OverallHealth = c.calculateOverallHealth(status)

	ctx.JSON(http.StatusOK, status)
}

// GetSchedulerOverview returns summary statistics for the dashboard
func (c *SchedulerController) GetSchedulerOverview(ctx *gin.Context) {
	overview := models.SchedulerOverview{
		TotalPlannedTasks: c.countPlannedTasks(),
		ActiveTasks:       c.countActiveTasks(),
		FailedTasks:       c.countFailedTasks(),
		CompletedToday:    c.countCompletedToday(),
		SuccessRate:       c.calculateSuccessRate(),
		AverageTaskTime:   c.calculateAverageTaskTime(),
	}

	ctx.JSON(http.StatusOK, overview)
}

// GetPlannedWork returns upcoming scheduled tasks from all systems
func (c *SchedulerController) GetPlannedWork(ctx *gin.Context) {
	var plannedTasks []models.PlannedTask

	// Get planned report tasks
	reportTasks := c.getPlannedReportTasks()
	plannedTasks = append(plannedTasks, reportTasks...)

	// Get planned scan tasks
	scanTasks := c.getPlannedScanTasks()
	plannedTasks = append(plannedTasks, scanTasks...)

	// Get planned auto-scan tasks
	autoScanTasks := c.getPlannedAutoScanTasks()
	plannedTasks = append(plannedTasks, autoScanTasks...)

	// Sort by scheduled time
	c.sortPlannedTasksByTime(plannedTasks)

	ctx.JSON(http.StatusOK, gin.H{
		"tasks": plannedTasks,
		"total": len(plannedTasks),
	})
}

// GetActiveTasks returns currently running tasks with progress
func (c *SchedulerController) GetActiveTasks(ctx *gin.Context) {
	var activeTasks []models.ActiveTask

	// Get active report tasks
	reportTasks := c.getActiveReportTasks()
	activeTasks = append(activeTasks, reportTasks...)

	// Get active scan tasks
	scanTasks := c.getActiveScanTasks()
	activeTasks = append(activeTasks, scanTasks...)

	// Get active auto-scan tasks
	autoScanTasks := c.getActiveAutoScanTasks()
	activeTasks = append(activeTasks, autoScanTasks...)

	ctx.JSON(http.StatusOK, gin.H{
		"tasks": activeTasks,
		"total": len(activeTasks),
	})
}

// GetFailedTasks returns failed tasks with retry information
func (c *SchedulerController) GetFailedTasks(ctx *gin.Context) {
	var failedTasks []models.FailedTask

	// Get failed report tasks
	reportTasks := c.getFailedReportTasks()
	failedTasks = append(failedTasks, reportTasks...)

	// Get failed scan tasks
	scanTasks := c.getFailedScanTasks()
	failedTasks = append(failedTasks, scanTasks...)

	// Get failed auto-scan tasks
	autoScanTasks := c.getFailedAutoScanTasks()
	failedTasks = append(failedTasks, autoScanTasks...)

	// Sort by failed time (most recent first)
	c.sortFailedTasksByTime(failedTasks)

	ctx.JSON(http.StatusOK, gin.H{
		"tasks": failedTasks,
		"total": len(failedTasks),
	})
}

// PauseScheduler pauses scheduler services
func (c *SchedulerController) PauseScheduler(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	response := models.TaskControlResponse{
		Success: false,
		Message: "Pause functionality not yet implemented",
	}

	// TODO: Implement pause functionality for each service
	log.Printf("Pause request received for service: %s", request.Service)

	ctx.JSON(http.StatusOK, response)
}

// ResumeScheduler resumes scheduler services
func (c *SchedulerController) ResumeScheduler(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	response := models.TaskControlResponse{
		Success: false,
		Message: "Resume functionality not yet implemented",
	}

	// TODO: Implement resume functionality for each service
	log.Printf("Resume request received for service: %s", request.Service)

	ctx.JSON(http.StatusOK, response)
}

// TriggerTask manually triggers a specific task
func (c *SchedulerController) TriggerTask(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	response := models.TaskControlResponse{
		Success: false,
		Message: "Trigger functionality not yet implemented",
	}

	// TODO: Implement trigger functionality for each task type
	log.Printf("Trigger request received for task: %s, type: %s", request.TaskID, request.TaskType)

	ctx.JSON(http.StatusOK, response)
}

// RetryTask retries a failed task
func (c *SchedulerController) RetryTask(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	response := models.TaskControlResponse{
		Success: false,
		Message: "Retry functionality not yet implemented",
	}

	// TODO: Implement retry functionality for each task type
	log.Printf("Retry request received for task: %s, type: %s", request.TaskID, request.TaskType)

	ctx.JSON(http.StatusOK, response)
}

// Helper methods for status aggregation

// getReportSchedulerStatus returns the status of the report scheduler service
func (c *SchedulerController) getReportSchedulerStatus() models.ServiceStatus {
	// TODO: Add method to SchedulerService to get status
	return models.ServiceStatus{
		Name:         "Report Scheduler",
		IsRunning:    true, // Placeholder - need to implement status check
		Status:       "running",
		LastActivity: time.Now().Format(time.RFC3339),
		ActiveTasks:  0,
		QueuedTasks:  0,
	}
}

// getUsageScannerStatus returns the status of the usage scanner service
func (c *SchedulerController) getUsageScannerStatus() models.ServiceStatus {
	// TODO: Add method to UsageScanner to get status
	return models.ServiceStatus{
		Name:         "Usage Scanner",
		IsRunning:    true, // Placeholder - need to implement status check
		Status:       "running",
		LastActivity: time.Now().Format(time.RFC3339),
		ActiveTasks:  0,
		QueuedTasks:  0,
	}
}

// getAutoScanServiceStatus returns the status of the auto scan service
func (c *SchedulerController) getAutoScanServiceStatus() models.ServiceStatus {
	// TODO: Add method to AutoScanService to get status
	return models.ServiceStatus{
		Name:         "Auto Scan Service",
		IsRunning:    true, // Placeholder - need to implement status check
		Status:       "running",
		LastActivity: time.Now().Format(time.RFC3339),
		ActiveTasks:  0,
		QueuedTasks:  0,
	}
}

// getWorkerPoolStatus returns the status of the worker pool
func (c *SchedulerController) getWorkerPoolStatus() models.ServiceStatus {
	// TODO: Add method to ScanWorkerPool to get status
	return models.ServiceStatus{
		Name:         "Worker Pool",
		IsRunning:    true, // Placeholder - need to implement status check
		Status:       "running",
		LastActivity: time.Now().Format(time.RFC3339),
		ActiveTasks:  0,
		QueuedTasks:  0,
	}
}

// calculateOverallHealth determines the overall health based on individual service statuses
func (c *SchedulerController) calculateOverallHealth(status models.SchedulerStatus) string {
	services := []models.ServiceStatus{
		status.ReportScheduler,
		status.UsageScanner,
		status.AutoScanService,
		status.WorkerPool,
	}

	healthyCount := 0
	errorCount := 0

	for _, service := range services {
		if service.Status == "running" && service.IsRunning {
			healthyCount++
		} else if service.Status == "error" {
			errorCount++
		}
	}

	if errorCount > 0 {
		return "unhealthy"
	} else if healthyCount == len(services) {
		return "healthy"
	} else {
		return "degraded"
	}
}

// Helper methods for counting tasks (placeholders for now)

func (c *SchedulerController) countPlannedTasks() int {
	var plannedTasks []models.PlannedTask

	// Get planned report tasks
	reportTasks := c.getPlannedReportTasks()
	plannedTasks = append(plannedTasks, reportTasks...)

	// Get planned scan tasks
	scanTasks := c.getPlannedScanTasks()
	plannedTasks = append(plannedTasks, scanTasks...)

	// Get planned auto-scan tasks
	autoScanTasks := c.getPlannedAutoScanTasks()
	plannedTasks = append(plannedTasks, autoScanTasks...)

	return len(plannedTasks)
}

func (c *SchedulerController) countActiveTasks() int {
	var activeTasks []models.ActiveTask

	// Get active report tasks
	reportTasks := c.getActiveReportTasks()
	activeTasks = append(activeTasks, reportTasks...)

	// Get active scan tasks
	scanTasks := c.getActiveScanTasks()
	activeTasks = append(activeTasks, scanTasks...)

	// Get active auto-scan tasks
	autoScanTasks := c.getActiveAutoScanTasks()
	activeTasks = append(activeTasks, autoScanTasks...)

	return len(activeTasks)
}

func (c *SchedulerController) countFailedTasks() int {
	var failedTasks []models.FailedTask

	// Get failed report tasks
	reportTasks := c.getFailedReportTasks()
	failedTasks = append(failedTasks, reportTasks...)

	// Get failed scan tasks
	scanTasks := c.getFailedScanTasks()
	failedTasks = append(failedTasks, scanTasks...)

	// Get failed auto-scan tasks
	autoScanTasks := c.getFailedAutoScanTasks()
	failedTasks = append(failedTasks, autoScanTasks...)

	return len(failedTasks)
}

func (c *SchedulerController) countCompletedToday() int {
	// TODO: Implement actual counting logic
	return 0
}

func (c *SchedulerController) calculateSuccessRate() int {
	// TODO: Implement actual calculation
	return 95 // Placeholder
}

func (c *SchedulerController) calculateAverageTaskTime() int {
	// TODO: Implement actual calculation
	return 120 // Placeholder: 2 minutes
}

// Helper methods for task aggregation (placeholders for now)

func (c *SchedulerController) getPlannedReportTasks() []models.PlannedTask {
	// TODO: Get scheduled report presets and convert to PlannedTask format
	return []models.PlannedTask{}
}

func (c *SchedulerController) getPlannedScanTasks() []models.PlannedTask {
	// TODO: Get scheduled scans and convert to PlannedTask format
	return []models.PlannedTask{}
}

func (c *SchedulerController) getPlannedAutoScanTasks() []models.PlannedTask {
	// TODO: Get auto scan jobs and convert to PlannedTask format
	return []models.PlannedTask{}
}

func (c *SchedulerController) getActiveReportTasks() []models.ActiveTask {
	// TODO: Get currently running report generations
	return []models.ActiveTask{}
}

func (c *SchedulerController) getActiveScanTasks() []models.ActiveTask {
	var activeTasks []models.ActiveTask

	if c.usageScanner == nil {
		return activeTasks
	}

	// Get active scans from usage scanner
	scanTasks, err := c.usageScanner.GetActiveScans()
	if err != nil {
		log.Printf("Error getting active scan tasks: %v", err)
		return activeTasks
	}

	return scanTasks
}

func (c *SchedulerController) getActiveAutoScanTasks() []models.ActiveTask {
	// TODO: Get currently running auto scan jobs
	return []models.ActiveTask{}
}

func (c *SchedulerController) getFailedReportTasks() []models.FailedTask {
	// TODO: Get failed report executions
	return []models.FailedTask{}
}

func (c *SchedulerController) getFailedScanTasks() []models.FailedTask {
	var failedTasks []models.FailedTask

	if c.usageScanner == nil {
		return failedTasks
	}

	// Get failed scans from usage scanner
	failedScans, err := c.usageScanner.GetFailedScans()
	if err != nil {
		log.Printf("Error getting failed scan tasks: %v", err)
		return failedTasks
	}

	// Convert UsageScanStatus to FailedTask
	for _, scan := range failedScans {
		// Create a task ID from group name and repo ID
		taskID := fmt.Sprintf("%s:%s", scan.GroupName, scan.RepoID)

		// Determine the primary error message
		errorMessage := "Scan completed with failures"
		if len(scan.FailedSources) > 0 {
			errorMessage = fmt.Sprintf("Failed to scan %d sources: %s",
				len(scan.FailedSources), scan.FailedSources[0].Error)
		}

		// Calculate retry count (for now, assume 0 - could be enhanced later)
		retryCount := 0
		canRetry := true

		failedTask := models.FailedTask{
			ID:           taskID,
			Type:         "scan",
			Name:         fmt.Sprintf("Usage Scan: %s", scan.GroupName),
			FailedAt:     scan.LastScanTime.Format(time.RFC3339),
			ErrorMessage: errorMessage,
			RetryCount:   retryCount,
			CanRetry:     canRetry,
			Repository:   scan.RepoID,
			GroupName:    scan.GroupName,
			LastAttempt:  scan.LastScanTime.Format(time.RFC3339),
			Metadata: map[string]interface{}{
				"sourcesTotal":     scan.SourcesTotal,
				"sourcesScanned":   scan.SourcesScanned,
				"sourcesCompleted": len(scan.CompletedSources),
				"sourcesFailed":    len(scan.FailedSources),
				"totalUsages":      scan.TotalUsages,
				"scanDuration":     scan.ScanDuration.Seconds(),
			},
		}

		failedTasks = append(failedTasks, failedTask)
	}

	return failedTasks
}

func (c *SchedulerController) getFailedAutoScanTasks() []models.FailedTask {
	// TODO: Get failed auto scan jobs
	return []models.FailedTask{}
}

// Utility methods for sorting

func (c *SchedulerController) sortPlannedTasksByTime(tasks []models.PlannedTask) {
	sort.Slice(tasks, func(i, j int) bool {
		timeI, errI := time.Parse(time.RFC3339, tasks[i].ScheduledFor)
		timeJ, errJ := time.Parse(time.RFC3339, tasks[j].ScheduledFor)

		// If parsing fails, put the failed item at the end
		if errI != nil {
			return false
		}
		if errJ != nil {
			return true
		}

		// Sort by scheduled time (earliest first)
		return timeI.Before(timeJ)
	})
}

func (c *SchedulerController) sortFailedTasksByTime(tasks []models.FailedTask) {
	sort.Slice(tasks, func(i, j int) bool {
		timeI, errI := time.Parse(time.RFC3339, tasks[i].FailedAt)
		timeJ, errJ := time.Parse(time.RFC3339, tasks[j].FailedAt)

		// If parsing fails, put the failed item at the end
		if errI != nil {
			return false
		}
		if errJ != nil {
			return true
		}

		// Sort by failed time (most recent first)
		return timeI.After(timeJ)
	})
}

// GetInterruptedScans returns all interrupted scans with details
func (c *SchedulerController) GetInterruptedScans(ctx *gin.Context) {
	// Initialize with empty slice to avoid null in JSON
	allScans := make([]models.InterruptedScan, 0)
	var stats models.InterruptedScansStats

	if c.usageScanner != nil {
		// Get interrupted scans
		interruptedScans, err := c.usageScanner.GetInterruptedScans()
		if err != nil {
			log.Printf("Error getting interrupted scans: %v", err)
		} else {
			for _, scan := range interruptedScans {
				interruptedScan := c.convertToInterruptedScan(scan, "interrupted")
				allScans = append(allScans, interruptedScan)
				stats.InterruptedCount++
			}
		}

		// Get failed scans
		failedScans, err := c.usageScanner.GetFailedScans()
		if err != nil {
			log.Printf("Error getting failed scans: %v", err)
		} else {
			for _, scan := range failedScans {
				failedScan := c.convertToInterruptedScan(scan, "failed")
				allScans = append(allScans, failedScan)
				stats.FailedCount++
			}
		}
	}

	// For now, pending restart is the same as interrupted count
	stats.PendingRestart = stats.InterruptedCount

	response := models.InterruptedScansResponse{
		Scans: allScans,
		Total: len(allScans),
		Stats: stats,
	}

	ctx.JSON(http.StatusOK, response)
}

// GetInterruptedScansStats returns statistics about interrupted scans
func (c *SchedulerController) GetInterruptedScansStats(ctx *gin.Context) {
	var stats models.InterruptedScansStats

	if c.usageScanner != nil {
		// Get interrupted scans count
		interruptedScans, err := c.usageScanner.GetInterruptedScans()
		if err != nil {
			log.Printf("Error getting interrupted scans: %v", err)
		} else {
			stats.InterruptedCount = len(interruptedScans)
		}

		// Get failed scans count
		failedScans, err := c.usageScanner.GetFailedScans()
		if err != nil {
			log.Printf("Error getting failed scans: %v", err)
		} else {
			stats.FailedCount = len(failedScans)
		}
	}

	// For now, pending restart is the same as interrupted count
	stats.PendingRestart = stats.InterruptedCount

	ctx.JSON(http.StatusOK, stats)
}

// RestartInterruptedScans restarts all interrupted scans
func (c *SchedulerController) RestartInterruptedScans(ctx *gin.Context) {
	log.Printf("Restart interrupted scans request received")

	// Get all interrupted scans
	interruptedScans, err := c.usageScanner.GetInterruptedScans()
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to retrieve interrupted scans: %v", err),
		}
		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	if len(interruptedScans) == 0 {
		response := models.TaskControlResponse{
			Success: true,
			Message: "No interrupted scans found to restart",
		}
		ctx.JSON(http.StatusOK, response)
		return
	}

	// Restart each interrupted scan
	restartedCount := 0
	var restartErrors []string

	for _, scan := range interruptedScans {
		log.Printf("Restarting interrupted scan for group %s in repo %s", scan.GroupName, scan.RepoID)

		// Cancel current scan if still in progress
		if scan.InProgress {
			if err := c.usageScanner.CancelScan(scan.GroupName, scan.RepoID); err != nil {
				log.Printf("Warning: Failed to cancel current scan for group %s in repo %s: %v",
					scan.GroupName, scan.RepoID, err)
			}
			// Wait a moment for cancellation to take effect
			time.Sleep(500 * time.Millisecond)
		}

		// Create a new scan request
		scanRequest := models.UsageScanRequest{
			GroupName: scan.GroupName,
			RepoID:    scan.RepoID,
			Force:     true, // Force restart even if recently scanned
		}

		// Start the new scan
		scanCtx := context.Background()
		if err := c.usageScanner.ScanGroupUsage(scanCtx, scanRequest); err != nil {
			errorMsg := fmt.Sprintf("Failed to restart scan for group '%s' in repo '%s': %v",
				scan.GroupName, scan.RepoID, err)
			restartErrors = append(restartErrors, errorMsg)
			log.Printf("Error: %s", errorMsg)
		} else {
			restartedCount++
			log.Printf("Successfully restarted scan for group %s in repo %s", scan.GroupName, scan.RepoID)
		}
	}

	// Prepare response based on results
	var response models.TaskControlResponse
	if len(restartErrors) == 0 {
		response = models.TaskControlResponse{
			Success: true,
			Message: fmt.Sprintf("Successfully restarted %d interrupted scan(s)", restartedCount),
		}
	} else if restartedCount > 0 {
		response = models.TaskControlResponse{
			Success: true,
			Message: fmt.Sprintf("Partially successful: restarted %d scan(s), %d error(s). Errors: %v",
				restartedCount, len(restartErrors), restartErrors),
		}
	} else {
		response = models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to restart any scans. Errors: %v", restartErrors),
		}
	}

	log.Printf("Restart interrupted scans completed: %d restarted, %d errors", restartedCount, len(restartErrors))
	ctx.JSON(http.StatusOK, response)
}

// ClearFailedScans clears all failed scan records
func (c *SchedulerController) ClearFailedScans(ctx *gin.Context) {
	log.Printf("Clear failed scans request received")

	// Get all failed scans
	failedScans, err := c.usageScanner.GetFailedScans()
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to retrieve failed scans: %v", err),
		}
		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	if len(failedScans) == 0 {
		response := models.TaskControlResponse{
			Success: true,
			Message: "No failed scans found to clear",
		}
		ctx.JSON(http.StatusOK, response)
		return
	}

	// Clear each failed scan
	clearedCount := 0
	var clearErrors []string

	for _, scan := range failedScans {
		log.Printf("Clearing failed scan for group %s in repo %s", scan.GroupName, scan.RepoID)

		if err := c.usageScanner.ClearUsageResults(scan.GroupName, scan.RepoID); err != nil {
			errorMsg := fmt.Sprintf("Failed to clear scan for group '%s' in repo '%s': %v",
				scan.GroupName, scan.RepoID, err)
			clearErrors = append(clearErrors, errorMsg)
			log.Printf("Error: %s", errorMsg)
		} else {
			clearedCount++
			log.Printf("Successfully cleared scan for group %s in repo %s", scan.GroupName, scan.RepoID)
		}
	}

	// Prepare response based on results
	var response models.TaskControlResponse
	if len(clearErrors) == 0 {
		response = models.TaskControlResponse{
			Success: true,
			Message: fmt.Sprintf("Successfully cleared %d failed scan(s)", clearedCount),
		}
	} else if clearedCount > 0 {
		response = models.TaskControlResponse{
			Success: true,
			Message: fmt.Sprintf("Partially successful: cleared %d scan(s), %d error(s). Errors: %v",
				clearedCount, len(clearErrors), clearErrors),
		}
	} else {
		response = models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to clear any scans. Errors: %v", clearErrors),
		}
	}

	log.Printf("Clear failed scans completed: %d cleared, %d errors", clearedCount, len(clearErrors))
	ctx.JSON(http.StatusOK, response)
}

// RestartSpecificScan restarts a specific scan by ID
func (c *SchedulerController) RestartSpecificScan(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Parse task ID to extract groupName and repoID
	// Expected format: "groupName:repoID"
	parts := strings.Split(request.TaskID, ":")
	if len(parts) != 2 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid task ID format. Expected format: 'groupName:repoID'",
		})
		return
	}

	groupName := parts[0]
	repoID := parts[1]

	log.Printf("Restart specific scan request received for group: %s, repo: %s", groupName, repoID)

	// Check if scan exists and get current status
	currentStatus, err := c.usageScanner.GetScanStatus(groupName, repoID)
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Scan not found for group '%s' in repository '%s'", groupName, repoID),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusNotFound, response)
		return
	}

	// If scan is currently in progress, cancel it first
	if currentStatus.InProgress {
		log.Printf("Canceling current scan for group %s in repo %s before restart", groupName, repoID)
		if err := c.usageScanner.CancelScan(groupName, repoID); err != nil {
			log.Printf("Warning: Failed to cancel current scan: %v", err)
		}
		// Wait a moment for cancellation to take effect
		time.Sleep(1 * time.Second)
	}

	// Create a new scan request
	scanRequest := models.UsageScanRequest{
		GroupName: groupName,
		RepoID:    repoID,
		Force:     true, // Force restart even if recently scanned
	}

	// Start the new scan
	scanCtx := context.Background()
	if err := c.usageScanner.ScanGroupUsage(scanCtx, scanRequest); err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to restart scan: %v", err),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	response := models.TaskControlResponse{
		Success: true,
		Message: fmt.Sprintf("Successfully restarted scan for group '%s' in repository '%s'", groupName, repoID),
		TaskID:  request.TaskID,
	}

	log.Printf("Successfully restarted scan for group %s in repo %s", groupName, repoID)
	ctx.JSON(http.StatusOK, response)
}

// convertToInterruptedScan converts a UsageScanStatus to InterruptedScan
func (c *SchedulerController) convertToInterruptedScan(scan models.UsageScanStatus, status string) models.InterruptedScan {
	// Calculate progress percentage
	var progress int
	if scan.SourcesTotal > 0 {
		progress = int(float64(scan.SourcesScanned) / float64(scan.SourcesTotal) * 100)
	}

	// Determine error message
	var errorMessage string
	if len(scan.FailedSources) > 0 {
		errorMessage = fmt.Sprintf("Failed to scan %d sources: %s",
			len(scan.FailedSources), scan.FailedSources[0].Error)
	} else if status == "interrupted" {
		errorMessage = "Scan was interrupted or orphaned"
	}

	return models.InterruptedScan{
		ID:           fmt.Sprintf("%s:%s", scan.GroupName, scan.RepoID),
		GroupName:    scan.GroupName,
		Repository:   scan.RepoID,
		Status:       status,
		StartedAt:    scan.LastScanTime.Format(time.RFC3339),
		Progress:     progress,
		ErrorMessage: errorMessage,
		CanRestart:   true,
		Metadata: map[string]interface{}{
			"sourcesTotal":     scan.SourcesTotal,
			"sourcesScanned":   scan.SourcesScanned,
			"sourcesCompleted": len(scan.CompletedSources),
			"sourcesFailed":    len(scan.FailedSources),
			"totalUsages":      scan.TotalUsages,
			"scanDuration":     scan.ScanDuration.Seconds(),
			"inProgress":       scan.InProgress,
		},
	}
}
