import { useEffect, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
// import { Progress } from "@/components/ui/progress" // Not needed for now
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  RefreshCw,
  Play,
  Pause,
  AlertCircle,
  CheckCircle,
  Clock,
  Activity,
  TrendingUp,
  Calendar,
  Zap
} from "lucide-react"
import api from "@/api/client"
import TaskHistoryLogs from "@/components/scheduler/TaskHistoryLogs"
import TaskManagementControls from "@/components/scheduler/TaskManagementControls"
import ActiveTasksMonitor from "@/components/scheduler/ActiveTasksMonitor"
import FailedTasksSection from "@/components/scheduler/FailedTasksSection"
import UsageTrackingSection from "@/components/scheduler/UsageTrackingSection"
import { GlobalScanMonitor } from "@/components/scheduler/GlobalScanMonitor"
import type {
  SchedulerStatus,
  SchedulerOverview,
  PlannedTask,
  ActiveTask,
  FailedTask,
  ServiceStatus
} from "@/types/scheduler"
import {
  ServiceStatusColors,
  OverallHealthColors,
  TaskTypeColors,
  TaskPriorityColors,
  formatRelativeTime,
  formatDuration,
  getProgressPercentage,
  getTaskTypeDisplayName,
  calculateTimeUntilScheduled
} from "@/types/scheduler"

// Small statistics card component
interface StatisticsCardProps {
  title: string
  value: number
  icon: React.ReactNode
  color: string
}

const StatisticsCard: React.FC<StatisticsCardProps> = ({ title, value, icon, color }) => (
  <Card className="h-16">
    <CardContent className="p-3">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-600">{title}</p>
          <p className={`text-lg font-bold ${color}`}>{value}</p>
        </div>
        <div className={color}>
          {icon}
        </div>
      </div>
    </CardContent>
  </Card>
)

const SchedulerDashboard = () => {
  const { toast } = useToast()

  // State management
  const [status, setStatus] = useState<SchedulerStatus | null>(null)
  const [overview, setOverview] = useState<SchedulerOverview | null>(null)
  const [plannedTasks, setPlannedTasks] = useState<PlannedTask[]>([])
  const [activeTasks, setActiveTasks] = useState<ActiveTask[]>([])
  const [failedTasks, setFailedTasks] = useState<FailedTask[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [activeTasksRefreshing, setActiveTasksRefreshing] = useState(false)
  const [failedTasksRefreshing, setFailedTasksRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null)
  const [logsExpanded, setLogsExpanded] = useState(false)

  // Load all scheduler data
  const loadSchedulerData = useCallback(async (showLoading = false) => {
    try {
      if (showLoading) {
        setLoading(true)
      } else {
        setRefreshing(true)
      }
      setError(null)

      // Load all data in parallel
      const [
        statusResponse,
        overviewResponse,
        plannedWorkResponse,
        activeTasksResponse,
        failedTasksResponse
      ] = await Promise.all([
        api.scheduler.getStatus(),
        api.scheduler.getOverview(),
        api.scheduler.getPlannedWork(),
        api.scheduler.getActiveTasks(),
        api.scheduler.getFailedTasks()
      ])

      setStatus(statusResponse)
      setOverview(overviewResponse)
      setPlannedTasks(plannedWorkResponse.tasks)
      setActiveTasks(activeTasksResponse.tasks)
      setFailedTasks(failedTasksResponse.tasks)
      setLastRefreshTime(new Date())
    } catch (err) {
      console.error('Failed to load scheduler data:', err)
      setError('Failed to load scheduler data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [])

  // Initial load
  useEffect(() => {
    loadSchedulerData(true)
  }, [loadSchedulerData])

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      loadSchedulerData(false)
    }, 30000)

    return () => clearInterval(interval)
  }, [loadSchedulerData])

  // Manual refresh with cache bypass
  const handleRefresh = async () => {
    try {
      setRefreshing(true)
      setError(null)

      // Load all data in parallel with cache bypass
      const [
        statusResponse,
        overviewResponse,
        plannedWorkResponse,
        activeTasksResponse,
        failedTasksResponse
      ] = await Promise.all([
        api.scheduler.refreshStatus(),
        api.scheduler.refreshOverview(),
        api.scheduler.refreshPlannedWork(),
        api.scheduler.refreshActiveTasks(),
        api.scheduler.refreshFailedTasks()
      ])

      setStatus(statusResponse)
      setOverview(overviewResponse)
      setPlannedTasks(plannedWorkResponse.tasks)
      setActiveTasks(activeTasksResponse.tasks)
      setFailedTasks(failedTasksResponse.tasks)
      setLastRefreshTime(new Date())

      toast({
        title: 'Success',
        description: 'Dashboard data refreshed successfully',
      })
    } catch (err) {
      console.error('Failed to refresh dashboard data:', err)
      toast({
        title: 'Error',
        description: 'Failed to refresh dashboard data',
        variant: 'destructive',
      })
    } finally {
      setRefreshing(false)
    }
  }

  // Refresh only active tasks
  const handleActiveTasksRefresh = async () => {
    try {
      setActiveTasksRefreshing(true)
      const activeTasksResponse = await api.scheduler.refreshActiveTasks()
      setActiveTasks(activeTasksResponse.tasks)
      setLastRefreshTime(new Date())
      toast({
        title: 'Success',
        description: 'Active tasks refreshed successfully',
      })
    } catch (err) {
      console.error('Failed to refresh active tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to refresh active tasks',
        variant: 'destructive',
      })
    } finally {
      setActiveTasksRefreshing(false)
    }
  }

  // Refresh only failed tasks
  const handleFailedTasksRefresh = async () => {
    try {
      setFailedTasksRefreshing(true)
      const failedTasksResponse = await api.scheduler.refreshFailedTasks()
      setFailedTasks(failedTasksResponse.tasks)
      setLastRefreshTime(new Date())
      toast({
        title: 'Success',
        description: 'Failed tasks refreshed successfully',
      })
    } catch (err) {
      console.error('Failed to refresh failed tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to refresh failed tasks',
        variant: 'destructive',
      })
    } finally {
      setFailedTasksRefreshing(false)
    }
  }

  // Map task type to service for API calls
  const getServiceForTaskType = (taskType: string): string => {
    switch (taskType) {
      case 'report':
        return 'report'
      case 'scan':
        return 'usage'
      case 'auto-scan':
        return 'auto-scan'
      default:
        return 'all'
    }
  }

  // Retry failed task
  const handleRetryTask = async (taskId: string) => {
    try {
      // Find the task to get its type
      const task = failedTasks.find(t => t.id === taskId)
      if (!task) {
        toast({
          title: 'Error',
          description: 'Task not found',
          variant: 'destructive',
        })
        return
      }

      const service = getServiceForTaskType(task.type)

      // Use the working restart-scan endpoint instead of the unimplemented retry endpoint
      const response = await api.scheduler.restartSpecificScan({
        action: 'retry',
        service: service as any,
        taskId: taskId,
        taskType: task.type,
      })

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || `Task ${task.name} has been retried`,
        })
        // Refresh data after successful retry
        await loadSchedulerData(false)
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to retry task',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Error retrying task:', err)
      toast({
        title: 'Error',
        description: 'Failed to retry task',
        variant: 'destructive',
      })
    }
  }

  // Delete failed task
  const handleDeleteTask = async (taskId: string) => {
    try {
      // Find the task to get its type
      const task = failedTasks.find(t => t.id === taskId)
      if (!task) {
        toast({
          title: 'Error',
          description: 'Task not found',
          variant: 'destructive',
        })
        return
      }

      // For now, we'll just refresh the data since there's no specific delete endpoint
      // In a real implementation, you might want to call a delete API
      toast({
        title: 'Info',
        description: 'Task removal functionality will be implemented based on backend support',
      })

      // Refresh data
      await loadSchedulerData(false)
    } catch (err) {
      console.error('Error deleting task:', err)
      toast({
        title: 'Error',
        description: 'Failed to delete task',
        variant: 'destructive',
      })
    }
  }

  // Handle task cancellation
  const handleCancelTask = async (taskId: string) => {
    try {
      const response = await api.scheduler.cancelTask({
        action: 'cancel',
        service: 'all', // Default to all services for cancel operations
        taskId: taskId,
      })

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message,
        })
        // Refresh the active tasks list
        handleActiveTasksRefresh()
      } else {
        toast({
          title: 'Error',
          description: response.message,
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to cancel task:', err)
      toast({
        title: 'Error',
        description: 'Failed to cancel task',
        variant: 'destructive',
      })
    }
  }

  // Clean up stale tasks
  const handleCleanupStaleTasks = async () => {
    try {
      const response = await api.scheduler.cleanupStaleTasks()

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || `Cleaned up ${response.totalCleaned} stale tasks`,
        })
        // Refresh active tasks to reflect the changes
        handleActiveTasksRefresh()
      } else {
        toast({
          title: 'Warning',
          description: response.message || 'Cleanup completed with some issues',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to cleanup stale tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to cleanup stale tasks',
        variant: 'destructive',
      })
    }
  }

  // Service status card component
  const ServiceStatusCard = ({ service }: { service: ServiceStatus }) => (
    <Card className="h-16">
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium text-gray-600">{service.name}</p>
            <div className="flex items-center space-x-1 mt-0.5">
              <Badge className={`${ServiceStatusColors[service.status]} border-0 text-xs px-1 py-0`}>
                {service.status}
              </Badge>
              {service.isRunning && (
                <span className="text-xs text-green-600">Running</span>
              )}
            </div>
          </div>
          <div className="h-4 w-4 flex items-center justify-center">
            {service.status === 'running' ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : service.status === 'error' ? (
              <AlertCircle className="h-4 w-4 text-red-600" />
            ) : (
              <Pause className="h-4 w-4 text-yellow-600" />
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Scheduler Dashboard</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Scheduler Dashboard</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
            <Button
              onClick={handleRefresh}
              className="mt-4"
              disabled={loading || refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${(loading || refreshing) ? 'animate-spin' : ''}`} />
              {(loading || refreshing) ? 'Retrying...' : 'Retry'}
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Scheduler Dashboard</h1>
        <div className="flex items-center space-x-2">
          {lastRefreshTime && (
            <span className="text-sm text-gray-500">
              Last updated: {formatRelativeTime(lastRefreshTime.toISOString())}
            </span>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleCleanupStaleTasks}
            className="text-orange-600 border-orange-300 hover:bg-orange-50"
          >
            <AlertCircle className="h-4 w-4 mr-2" />
            Cleanup Stale
          </Button>
        </div>
      </div>

      {/* Overall Health Status & Statistics */}
      {status && overview && (
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>System Overview</span>
              <Badge className={`${OverallHealthColors[status.overallHealth]} border-0 ml-2`}>
                {status.overallHealth}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
              <ServiceStatusCard service={status.reportScheduler} />
              <ServiceStatusCard service={status.usageScanner} />
              <ServiceStatusCard service={status.autoScanService} />
              <ServiceStatusCard service={status.workerPool} />
              <StatisticsCard
                title="Planned"
                value={overview.totalPlannedTasks}
                icon={<Calendar className="h-4 w-4" />}
                color="text-blue-600"
              />
              <StatisticsCard
                title="Active"
                value={overview.activeTasks}
                icon={<Zap className="h-4 w-4" />}
                color="text-green-600"
              />
              <StatisticsCard
                title="Failed"
                value={overview.failedTasks}
                icon={<AlertCircle className="h-4 w-4" />}
                color="text-red-600"
              />
              <StatisticsCard
                title="Completed"
                value={overview.completedToday}
                icon={<CheckCircle className="h-4 w-4" />}
                color="text-purple-600"
              />
            </div>
          </CardContent>
        </Card>
      )}



      {/* Main Content Tabs */}
      <Tabs defaultValue="planned" className="space-y-4">
        <TabsList>
          <TabsTrigger value="planned">Planned Work ({plannedTasks?.length || 0})</TabsTrigger>
          <TabsTrigger value="active">Active Tasks ({activeTasks?.length || 0})</TabsTrigger>
          <TabsTrigger value="monitor">Real-time Monitor</TabsTrigger>
          <TabsTrigger value="failed">Failed Tasks ({failedTasks?.length || 0})</TabsTrigger>
          <TabsTrigger value="logs">Task History & Logs</TabsTrigger>
          <TabsTrigger value="usage">Usage Tracking</TabsTrigger>
          <TabsTrigger value="controls">Management Controls</TabsTrigger>
        </TabsList>

        {/* Planned Work Tab */}
        <TabsContent value="planned">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Scheduled Tasks</CardTitle>
              <CardDescription>
                Tasks scheduled to run in the near future
              </CardDescription>
            </CardHeader>
            <CardContent>
              {(plannedTasks?.length || 0) === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No planned tasks found
                </div>
              ) : (
                <div className="space-y-4">
                  {(plannedTasks || []).map((task) => (
                    <div
                      key={task.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <Badge className={`${TaskTypeColors[task.type]} border-0`}>
                            {getTaskTypeDisplayName(task.type)}
                          </Badge>
                          <Badge className={`${TaskPriorityColors[task.priority]} border-0`}>
                            {task.priority}
                          </Badge>
                          <h3 className="font-medium">{task.name}</h3>
                        </div>
                        <div className="text-sm text-gray-500">
                          {calculateTimeUntilScheduled(task.scheduledFor)}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{task.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Scheduled: {new Date(task.scheduledFor).toLocaleString()}</span>
                        {task.repository && <span>Repository: {task.repository}</span>}
                        {task.groupName && <span>Group: {task.groupName}</span>}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Active Tasks Tab */}
        <TabsContent value="active">
          <ActiveTasksMonitor
            tasks={activeTasks}
            loading={activeTasksRefreshing}
            onRefresh={handleActiveTasksRefresh}
            onCancelTask={handleCancelTask}
            enableRealTimeMonitoring={true}
          />
        </TabsContent>

        {/* Real-time Monitor Tab */}
        <TabsContent value="monitor">
          <div className="space-y-6">
            {/* Global Scan Monitor - Comprehensive real-time scan monitoring */}
            <GlobalScanMonitor
              maxDisplayedScans={20}
              autoRefresh={true}
            />

            {/* Additional monitoring components can be added here */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Real-time Monitoring Dashboard
                </CardTitle>
                <CardDescription>
                  This dashboard provides comprehensive real-time monitoring of all scan operations across the system.
                  Use the "Active Tasks" tab to view and manage scheduler tasks.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                    <span>Running Scans: Real-time progress tracking</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <span>Completed Scans: Recent completion history</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <span>Failed Scans: Error tracking and alerts</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Failed Tasks Tab */}
        <TabsContent value="failed">
          <FailedTasksSection
            tasks={failedTasks}
            loading={failedTasksRefreshing}
            onRefresh={handleFailedTasksRefresh}
            onRetryTask={handleRetryTask}
            onDeleteTask={handleDeleteTask}
          />
        </TabsContent>

        {/* Task History & Logs Tab - Integrated scan logs functionality */}
        <TabsContent value="logs">
          <TaskHistoryLogs
            isExpanded={logsExpanded}
            onToggleExpanded={() => setLogsExpanded(!logsExpanded)}
          />
        </TabsContent>

        {/* Usage Tracking Tab - Integrated usage tracking settings */}
        <TabsContent value="usage">
          <UsageTrackingSection />
        </TabsContent>

        {/* Management Controls Tab */}
        <TabsContent value="controls">
          <TaskManagementControls
            schedulerStatus={status || undefined}
            onStatusUpdate={() => loadSchedulerData(false)}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default SchedulerDashboard
